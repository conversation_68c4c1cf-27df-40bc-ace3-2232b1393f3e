mirrorsRegistry: &mirrorsRegistry "mirrors.tencent.com"
bcsRegistry: &bcsRegistry "mirrors.tencent.com/archieai-test/bcs"
global:
  imageRegistry: ""
  telnet:
    registry: *bcsRegistry
  serviceMonitor:
    enabled: false
  storageClass: standard
bcs-init:
  enabled: false
bcs-alert-manager:
  enabled: false
bcs-api-gateway:
  apisix:
    registry: *bcsRegistry
  gateway:
    registry: *bcsRegistry
bcs-cluster-manager:
  replicaCount: 1
  image:
    registry: *bcsRegistry
bcs-storage:
  image:
    registry: *bcsRegistry
bcs-user-manager:
  image:
    registry: *bcsRegistry
  mysql:
    image:
      registry: *mirrorsRegistry
    persistence:
      enabled: true
    size: 10Gi
rabbitmq:
  image:
    registry: *mirrorsRegistry
  persistence:
    enabled: true
    size: 10Gi
zookeeper:
  image:
    registry: *mirrorsRegistry
  persistence:
    enabled: true
    size: 10Gi
  service:
    type: NodePort
etcd:
  image:
    registry: *mirrorsRegistry
  persistence:
    enabled: true
    size: 10Gi
mongodb:
  image:
    registry: *mirrorsRegistry
  persistence:
    enabled: true
    size: 10Gi
cert-manager:
  image:
    registry: *bcsRegistry
  webhook:
    image:
      registry: *bcsRegistry
  cainjector:
    image:
      registry: *bcsRegistry
bcs-k8s:
  bcs-gamedeployment-operator:
    image:
      registry: *bcsRegistry
  bcs-gamestatefulset-operator:
    image:
      registry: *bcsRegistry
  bcs-hook-operator:
    image:
      registry: *bcsRegistry
  bcs-k8s-watch:
    image:
      registry: *bcsRegistry
  bcs-kube-agent:
    image:
      registry: *bcsRegistry
  bcs-webhook-server:
    image:
      registry: *bcsRegistry
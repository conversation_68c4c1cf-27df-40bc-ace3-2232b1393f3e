apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    tbs/name: __crm_name__
  name: __crm_name__
  namespace: __crm_namespace__
spec:
  replicas: __crm_instance__
  selector:
    matchLabels:
      tbs/name: __crm_name__
  template:
    metadata:
      labels:
        tbs/name: __crm_name__
      name: __crm_name__
      annotations:
        randhostport.webhook.bkbcs.tencent.com: "true"
        randcontainerport.randhostport.webhook.bkbcs.tencent.com: true
        ports.randhostport.webhook.bkbcs.tencent.com: "__crm_rand_port_names__"
    spec:
      __crm_host_network__
      containers:
      - name: __crm_name__
        image: __crm_image__
        ports: __crm_ports__
        env: __crm_env__
        - name: HOST_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts: __crm_volume_mounts__
        resources:
          requests:
            memory: "__crm_mem__Mi"
            cpu: "__crm_cpu__m"
            __crm_storage__
          limits:
            memory: "__crm_limit_mem__Mi"
            cpu: "__crm_limit_cpu__m"
            __crm_limit_storage__
      volumes: __crm_volumes__
      nodeSelector:
        __crm_platform_key__: __crm_platform__
        __crm_city_key__: __crm_city__
# Default values for bktbs.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
nameOverride: ""
fullnameOverride: ""

# global:
#   imageRegistry: myRegistryName
#   imagePullSecrets:
#     - myRegistryKeySecretName
#   storageClass: myStorageClass

## Add labels to all the deployed resources
##
commonLabels: {}

## Add annotations to all the deployed resources
##
commonAnnotations: {}

## Specifies whether RBAC resources should be created
##
rbac:
  create: true

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

ServiceMonitor:
  enable: false

bkLogConfig:
  enable: false
  server: 
    dataId: 1
  gateway:
    dataId: 1
  dashboard:
    dataId: 1

ingress:
  enabled: true
  className: ""
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "10240m"
    # kubernetes.io/tls-acme: "true"

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

nginx-ingress-controller:
  ## 是否部署nginx-ingress-controller
  enabled: true
  defaultBackend:
    enabled: false

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

etcd:
  enabled: true

  ## Authentication parameters
  ##
  auth:
    ## Role-based access control parameters
    ## ref: https://etcd.io/docs/current/op-guide/authentication/
    ##
    rbac:
      ## @param auth.rbac.enabled Switch to enable RBAC authentication
      ##
      enabled: false

    ## TLS authentication for client-to-server communications
    ## ref: https://etcd.io/docs/current/op-guide/security/
    ##
    client:
      ## @param auth.client.secureTransport Switch to encrypt client-to-server communications using TLS certificates
      ##
      secureTransport: false
      ## @param auth.client.useAutoTLS Switch to automatically create the TLS certificates
      ##
      useAutoTLS: false
      ## @param auth.client.existingSecret Name of the existing secret containing the TLS certificates for client-to-server communications
      ##
      existingSecret: ""
      ## @param auth.client.enableAuthentication Switch to enable host authentication using TLS certificates. Requires existing secret
      ##
      enableAuthentication: false
      ## @param auth.client.certFilename Name of the file containing the client certificate
      ##
      certFilename: cert.pem
      ## @param auth.client.certKeyFilename Name of the file containing the client certificate private key
      ##
      certKeyFilename: key.pem
      ## @param auth.client.caFilename Name of the file containing the client CA certificate
      ## If not specified and `auth.client.enableAuthentication=true` or `auth.rbac.enabled=true`, the default is is `ca.crt`
      ##
      caFilename: ""

##
## External Etcd Configuration
##
## All of these values are ignored when etcd.enabled is set to true
##
externalEtcd:
  ## Database server host and port
  ##
  address: "http://127.0.0.1:2379"


##
## MariaDB chart configuration
##
## https://github.com/bitnami/charts/blob/master/bitnami/mariadb/values.yaml
##
mariadb:
  enabled: true
  ## MariaDB architecture. Allowed values: standalone or replication
  ##
  architecture: standalone
  ## Custom user/db credentials
  ##
  auth:
    ## MariaDB root password
    ## ref: https://github.com/bitnami/bitnami-docker-mariadb#setting-the-root-password-on-first-run
    ##
    rootPassword: ""
    ## MariaDB custom user and database
    ## ref: https://github.com/bitnami/bitnami-docker-mariadb/blob/master/README.md#creating-a-database-on-first-run
    ## ref: https://github.com/bitnami/bitnami-docker-mariadb/blob/master/README.md#creating-a-database-user-on-first-run
    ##
    username: tbs
    password: tbs-password
    ## Database to create
    ## ref: https://github.com/bitnami/bitnami-docker-mariadb#creating-a-database-on-first-run
    ##
    database: tbs
    forcePassword: false
    usePasswordFiles: false

##
## External Database Configuration
##
## All of these values are ignored when mariadb.enabled is set to true
##
externalDatabase:
  ## Database server host and port
  ##
  host: localhost
  port: 3306
  ## Database driver and scheme
  ##
  # driver:
  # scheme:
  usernmae: ""
  password: ""
  database: ""

server:
  enabled: true
  host: bktbs.com
  servingPort: 80
  etcdRootPath: /tbs_server

  # image configuration
  image:
    registry: mirrors.tencent.com
    repository: bkce/bktbs/service/bktbs-server
    tag: 1.0.8
    pullPolicy: IfNotPresent
    pullSecrets: []

  # engine disttask queue list
  disttask_queue_list:
    - K8S://default
    - K8S_WIN://default

  # log configuration
  log:
    verbose: 3
    alsoLogToStdErr: true

  # bcs configuration
  bcs:
    clusterID: ""
    apiToken: ""
    apiAddress: https://127.0.0.1:8443
    cpuPerInstance: 8
    memPerInstance: 16384
    groupLabelKey: bk-turbo-az
    platformLabelKey: kubernetes.io/os
    disableWinHostNetwork: true

  # service configuration
  service:
    type: ClusterIP
    port: 30111
    nodePort: 30111
  metrics:
    type: ClusterIP
    port: 30112
    nodePort: 30112
  # k8s configuration
  replicaCount: 1
  hostAliases: []
  resources:
    limits: {}
    requests: {}
  containerSecurityContext:
    enabled: false
    runAsUser: 1001
    runAsNonRoot: true
  podSecurityContext:
    enabled: false
    fsGroup: 1001
  podAffinityPreset: ""
  podAntiAffinityPreset: soft
  nodeAffinityPreset:
    type: ""
    key: ""
    values: []
  affinity: {}
  nodeSelector: {}
  tolerations: []
  podLabels: {}
  podAnnotations: {}
  priorityClassName: ""

gateway:
  enabled: true
  etcdRootPath: /tbs_gateway
  # initWorkers describe the init worker setting and images
  initWorkers:
    cc:
      name:
      image:

  # image configuration
  image:
    registry: mirrors.tencent.com
    repository: bkce/bktbs/service/bktbs-gateway
    tag: 1.0.8
    pullPolicy: IfNotPresent
    pullSecrets: []

  # log configuration
  log:
    verbose: 3
    alsoLogToStdErr: true

  # service configuration
  service:
    type: ClusterIP
    port: 30113
    nodePort: 30113

  # k8s configuration
  replicaCount: 1
  hostAliases: []
  resources:
    limits: {}
    requests: {}
  containerSecurityContext:
    enabled: false
    runAsUser: 1001
    runAsNonRoot: true
  podSecurityContext:
    enabled: false
    fsGroup: 1001
  podAffinityPreset: ""
  podAntiAffinityPreset: soft
  nodeAffinityPreset:
    type: ""
    key: ""
    values: []
  affinity: {}
  nodeSelector: {}
  tolerations: []
  podLabels: {}
  podAnnotations: {}
  priorityClassName: ""

dashboard:
  enabled: true

  # image configuration
  image:
    registry: mirrors.tencent.com
    repository: bkce/bktbs/service/bktbs-dashboard
    tag: 1.0.8
    pullPolicy: IfNotPresent
    pullSecrets: []

  # log configuration
  log:
    verbose: 3
    alsoLogToStdErr: true

  # service configuration
  service:
    type: ClusterIP
    port: 30114
    nodePort: ""

  # k8s configuration
  replicaCount: 1
  hostAliases: []
  resources:
    limits: {}
    requests: {}
  containerSecurityContext:
    enabled: false
    runAsUser: 1001
    runAsNonRoot: true
  podSecurityContext:
    enabled: false
    fsGroup: 1001
  podAffinityPreset: ""
  podAntiAffinityPreset: soft
  nodeAffinityPreset:
    type: ""
    key: ""
    values: []
  affinity: {}
  nodeSelector: {}
  tolerations: []
  podLabels: {}
  podAnnotations: {}
  priorityClassName: ""

downloader:
  enabled: true

  # image configuration
  image:
    registry: mirrors.tencent.com
    repository: bkce/bktbs/service/bktbs-downloader
    tag: 1.0.8
    pullPolicy: IfNotPresent
    pullSecrets: []

  # service configuration
  service:
    type: ClusterIP
    port: 80
    nodePort: ""

  # k8s configuration
  replicaCount: 1
  hostAliases: []
  resources:
    limits: {}
    requests: {}
  containerSecurityContext:
    enabled: false
    runAsUser: 1001
    runAsNonRoot: true
  podSecurityContext:
    enabled: false
    fsGroup: 1001
  podAffinityPreset: ""
  podAntiAffinityPreset: soft
  nodeAffinityPreset:
    type: ""
    key: ""
    values: []
  affinity: {}
  nodeSelector: {}
  tolerations: []
  podLabels: {}
  podAnnotations: {}
  priorityClassName: ""

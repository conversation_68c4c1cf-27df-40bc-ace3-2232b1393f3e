{{- if .Values.gateway.enabled -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.fullname" . }}-gateway
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: gateway
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: gateway
  replicas: 1
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: gateway
        {{- if .Values.gateway.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.gateway.podLabels "context" $) | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ template "bktbs.serviceAccountName" . }}
      {{- include "bktbs.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.gateway.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.gateway.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.gateway.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.gateway.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.gateway.podAffinityPreset "component" "gateway" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.gateway.podAntiAffinityPreset "component" "gateway" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.gateway.nodeAffinityPreset.type "key" .Values.gateway.nodeAffinityPreset.key "values" .Values.gateway.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.gateway.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.gateway.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.gateway.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.gateway.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.gateway.priorityClassName }}
      priorityClassName: {{ .Values.gateway.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.gateway.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.gateway.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      containers:
        - name: gateway
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.gateway.image "global" .Values.global) }}
          imagePullPolicy: {{ .Values.gateway.image.pullPolicy }}
          {{- if .Values.gateway.resources }}
          resources: {{- toYaml .Values.gateway.resources | nindent 12 }}
          {{- end }}
          command: ["/data/workspace/start.sh"]
          args:
            - -f
            - /data/config/gateway.json
          env:
            - name: BK_FULLNAME
              value: {{ include "bktbs.mariadb.host" . }}
            - name: BK_TBS_LOCAL_IP
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          ports:
            - name: http
              containerPort: 30113
              protocol: TCP
          volumeMounts:
            - name: config-volume
              mountPath: /data/config/
          livenessProbe:
            httpGet:
              path: api/v1/health
              port: http
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: api/v1/health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "common.names.fullname" . }}-gateway-config
{{- end }}
{{- if .Values.gateway.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.fullname" . }}-gateway-config
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: gateway
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
data:
  gateway.json: |-
    {
      "etcd_endpoints": "{{ include "bktbs.etcd.address" . }}",
      "etcd_root_path": "{{ .Values.gateway.etcdRootPath }}",
      "etcd_ca_file": "",
      "etcd_cert_file": "",
      "etcd_key_file": "",
      "etcd_key_password": "",
      "v": {{ .Values.gateway.log.verbose }},
      "alsologtostderr": {{ default "true" .Values.gateway.log.alsoLogToStdErr }},
      "address": "0.0.0.0",
      "port": 30113,
      "disttask_mysql": {
        "disttask_enable":true,
        "disttask_mysql": "{{ include "bktbs.mariadb.address" . }}",
        "disttask_mysql_db": "{{ include "bktbs.mariadb.database" . }}",
        "disttask_mysql_user": "{{ include "bktbs.mariadb.username" . }}",
        "disttask_mysql_pwd": "{{ include "bktbs.mariadb.password" . }}"
      }
    }
{{- end }}

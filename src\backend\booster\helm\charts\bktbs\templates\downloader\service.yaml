{{- if .Values.downloader.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}-downloader
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: downloader
  type: {{ .Values.downloader.service.type }}
  {{- if eq .Values.downloader.service.type "LoadBalancer" }}
  {{- if .Values.downloader.service.loadBalancerIP }}
  loadBalancerIP: {{ default "" .Values.downloader.service.loadBalancerIP | quote }}
  {{- end }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.downloader.service.port }}
      {{- if and .Values.downloader.service.nodePort (or (eq .Values.downloader.service.type "NodePort") (eq .Values.downloader.service.type "LoadBalancer")) }}
      nodePort: {{ .Values.downloader.service.nodePort }}
      {{- else }}
      nodePort: null
      {{- end }}
      targetPort: http
{{- end }}

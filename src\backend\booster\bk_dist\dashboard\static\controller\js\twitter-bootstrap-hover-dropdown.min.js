/*
 * Project: Twitter Bootstrap Hover Dropdown
 * Author: <PERSON>
 * Contributors: <PERSON><PERSON>
 *
 * Dependencies: Twitter Bootstrap's Dropdown plugin, jQuery
 *
 * A simple plugin to enable twitter bootstrap dropdowns to active on hover and provide a nice user experience.
 *
 * License: MIT
 *
 * http://cameronspear.com/blog/twitter-bootstrap-dropdown-on-hover-plugin/
 */
(function(e, t, n) {
    var r = e();
    e.fn.dropdownHover = function(n) {
        r = r.add(this.parent());
        return this.each(function() {
            var i = e(this),
                s = i.parent(),
                o = {
                    delay: 500,
                    instantlyCloseOthers: !0
                },
                u = {
                    delay: e(this).data("delay"),
                    instantlyCloseOthers: e(this).data("close-others")
                },
                a = e.extend(!0, {}, o, n, u),
                f;
            s.hover(function(e) {
                if (!s.hasClass("open") && !i.is(e.target)) return !0;
                a.instantlyCloseOthers === !0 && r.removeClass("open");
                t.clearTimeout(f);
                s.addClass("open")
            }, function() {
                f = t.setTimeout(function() {
                    s.removeClass("open")
                }, a.delay)
            });
            i.hover(function() {
                a.instantlyCloseOthers === !0 && r.removeClass("open");
                t.clearTimeout(f);
                s.addClass("open")
            });
            s.find(".dropdown-submenu").each(function() {
                var n = e(this),
                    r;
                n.hover(function() {
                    t.clearTimeout(r);
                    n.children(".dropdown-menu").show();
                    n.siblings().children(".dropdown-menu").hide()
                }, function() {
                    var e = n.children(".dropdown-menu");
                    r = t.setTimeout(function() {
                        e.hide()
                    }, a.delay)
                })
            })
        })
    };
    e(document).ready(function() {
        e('[data-hover="dropdown"]').dropdownHover()
    })
})(jQuery, this);
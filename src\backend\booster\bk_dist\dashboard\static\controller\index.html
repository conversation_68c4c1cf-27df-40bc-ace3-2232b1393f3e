<!DOCTYPE html>
<html>

<head>
    <title>编译加速数据分析</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="css/font-awesome.css" rel="stylesheet">
    <link href="css/bootstrap_noresponsive.css" rel="stylesheet">
    <link href="css/bk.css" rel="stylesheet">
    <link rel="stylesheet" href="css/bootstrap-admin-theme.css">

    <link rel="stylesheet" href="css/index.css">
    <script type="text/javascript" src="js/vue.min.js"></script>
    <!-- 以下两个插件用于在IE8以及以下版本浏览器支持HTML5元素和媒体查询，如果不需要用可以移除 -->
    <!--[if lt IE 9]>
    <script src="js/html5shiv.min.js"></script>
    <script src="js/respond.min.js"></script>
    <![endif]-->
</head>

<body class="bootstrap-admin-with-small-navbar">
<!-- main / large navbar -->
<nav class="navbar navbar-default navbar-fixed-top bootstrap-admin-navbar bootstrap-admin-navbar-under-small"
     role="navigation">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse"
                            data-target=".main-navbar-collapse">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="javascript:;">Build-Booster Analysis Dashboard 编译加速数据分析</a>
                </div>
                <!-- /.navbar-collapse -->
            </div>
        </div>
    </div>
    <!-- /.container -->
</nav>

<div class="container">
    <!-- left, vertical navbar & content -->
    <div class="row">
        <!-- content -->
        <div class="col-md-12">

            <div class="row">
                <div class="col-lg-12">
                    <div class="alert alert-success bootstrap-admin-alert">
                        <button type="button" class="close" data-dismiss="alert">×</button>
                        <h4>欢迎使用编译加速数据分析</h4>
                    </div>
                </div>
            </div>

            <div class="data-holder">
                <div class="row">
                    <div class="col-md-12">
                        <ul class="list-group" v-cloak>
                            <li v-for="(index, item) in work_list" class="list-group-item">
                                <span class="badge">{{ date_format(item.registered_time) }}</span>
                                <i class="fa fa-tasks" v-on:click="start(item.work_id)"
                                   style="cursor: pointer;width: 70px">点击查看</i>
                                <span>WorkID: {{ item.work_id }}</span>
                            </li>
                        </ul>
                    </div>
                </div>


                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <div class="text-muted bootstrap-admin-box-title">基础信息</div>
                                <div class="pull-right"></div>
                            </div>
                            <div class="bootstrap-admin-panel-content" style="height: 220px;">
                                <section class="panel panel-box">
                                    <div class="list-justified-container" v-cloak style="padding:0px 0">
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ work_status }}</p>
                                                <p class="text-muted">编译状态</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ total_time }}</p>
                                                <p class="text-muted">总时长</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ apply_time }}</p>
                                                <p class="text-muted">申请资源</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ real_time }}</p>
                                                <p class="text-muted">实际编译</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f20">{{ current_data.job_remote_ok }}</p>
                                                <p class="text-muted">远程编译文件</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.real_remote_error }}</p>
                                                <p class="text-muted">远程失败文件</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.job_local_ok }}</p>
                                                <p class="text-muted">本地编译文件</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.job_local_error }}</p>
                                                <p class="text-muted">本地失败文件</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;display:none">
                                            <li>
                                                <p class="f20">{{ current_task.client_cpu }}</p>
                                                <p class="text-muted">本地CPU</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_task.cpu_total }}</p>
                                                <p class="text-muted">远程CPU</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_task.queue_name }}</p>
                                                <p class="text-muted">加速集群地区</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_task.source_ip }}</p>
                                                <p class="text-muted">发起机器IP</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;display:none">
                                            <li style="width: 240px !important;">
                                                <p class="f20">{{ current_task.client_version }}</p>
                                                <p class="text-muted">client版本</p>
                                            </li>
                                            <li style="width: 240px !important;">
                                                <p class="f20">{{ current_task.worker_version }}</p>
                                                <p class="text-muted">worker版本</p>
                                            </li>
                                        </ul>
                                    </div>
                                </section>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <div class="text-muted bootstrap-admin-box-title">运行时信息</div>
                                <div class="pull-right"></div>
                            </div>
                            <div class="bootstrap-admin-panel-content" style="height: 420px;">
                                <section class="panel panel-box">
                                    <div class="list-justified-container" v-cloak style="padding:0px 0">
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ current_data.remote_work_waiting }}</p>
                                                <p class="text-muted">远程处理等待中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.remote_work_holding }}</p>
                                                <p class="text-muted">远程处理持锁中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.remote_work_held }}</p>
                                                <p class="text-muted">远程处理累计持锁</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.remote_work_released }}</p>
                                                <p class="text-muted">远程处理累计释放锁</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ current_data.pre_work_waiting }}</p>
                                                <p class="text-muted">本地预处理等待中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.pre_work_holding }}</p>
                                                <p class="text-muted">本地预处理持锁中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.pre_work_held }}</p>
                                                <p class="text-muted">本地预处理累计持锁</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.pre_work_released }}</p>
                                                <p class="text-muted">本地预处理累计释放锁</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ current_data.local_work_waiting }}</p>
                                                <p class="text-muted">本地处理等待中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.local_work_holding }}</p>
                                                <p class="text-muted">本地处理持锁中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.local_work_held }}</p>
                                                <p class="text-muted">本地处理累计持锁</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.local_work_released }}</p>
                                                <p class="text-muted">本地处理累计释放锁</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ current_data.post_work_waiting }}</p>
                                                <p class="text-muted">本地后置处理等待中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.post_work_holding }}</p>
                                                <p class="text-muted">本地后置处理持锁中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.post_work_held }}</p>
                                                <p class="text-muted">本地后置处理累计持锁</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.post_work_released }}</p>
                                                <p class="text-muted">本地后置处理累计释放锁</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ current_data.dependent_waiting }}</p>
                                                <p class="text-muted">等待公共依赖文件中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.sending }}</p>
                                                <p class="text-muted">发送依赖文件中</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ current_data.receiving }}</p>
                                                <p class="text-muted">接收结果文件中</p>
                                            </li>
                                            <li>
                                                <p class="f20"></p>
                                                <p class="text-muted">-</p>
                                            </li>
                                        </ul>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-for="(index, item) in work_list">
                    <div v-if="current == item.work_id" v-cloak>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <div class="text-muted bootstrap-admin-box-title">并发数据曲线</div>
                                    </div>
                                    <div class="bootstrap-admin-panel-content" style="height:400px;">
                                        <canvas id="concurrency_chart-{{ item.work_id }}" style="padding:10px;"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <div class="text-muted bootstrap-admin-box-title">文件处理曲线</div>
                                    </div>
                                    <div class="bootstrap-admin-panel-content" style="height:400px;">
                                        <canvas id="sum_chart-{{ item.work_id }}" style="padding:10px;"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="js/jquery-1.10.2.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/twitter-bootstrap-hover-dropdown.min.js"></script>
<script type="text/javascript" src="js/pako.js"></script>
<script type="text/javascript" src="js/index.js"></script>
<script src="js/chart.min.js"></script>
<script src="js/bk.js"></script>
<script type="text/javascript">
</script>
</body>

</html>
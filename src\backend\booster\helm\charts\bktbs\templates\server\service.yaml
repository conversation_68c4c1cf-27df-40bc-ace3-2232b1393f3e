{{- if .Values.server.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}-server
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: server
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: server
  type: {{ .Values.server.service.type }}
  {{- if eq .Values.server.service.type "LoadBalancer" }}
  {{- if .Values.server.service.loadBalancerIP }}
  loadBalancerIP: {{ default "" .Values.server.service.loadBalancerIP | quote }}
  {{- end }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.server.service.port }}
      {{- if and .Values.server.service.nodePort (or (eq .Values.server.service.type "NodePort") (eq .Values.server.service.type "LoadBalancer")) }}
      nodePort: {{ .Values.server.service.nodePort }}
      {{- else }}
      nodePort: null
      {{- end }}
      targetPort: http
    - name: metrics
      port: {{ .Values.server.metrics.port }}
      {{- if and .Values.server.metrics.nodePort (or (eq .Values.server.metrics.type "NodePort") (eq .Values.server.metrics.type "LoadBalancer")) }}
      nodePort: {{ .Values.server.metrics.nodePort }}
      {{- else }}
      nodePort: null
      {{- end }}
      targetPort: {{ .Values.server.metrics.port }}
{{- end }}

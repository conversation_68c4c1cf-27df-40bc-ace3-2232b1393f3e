apiVersion: batch/v1
kind: Job
metadata:
  name: init-worker
  annotations:
    "helm.sh/hook": "post-install"
spec:
  template:
    spec:
      containers:
        - name: post-install
          image: ellerbrock/alpine-bash-curl-ssl:latest
          imagePullPolicy: IfNotPresent

          command: ['sh', '-c', '/data/config/init.sh']
          volumeMounts:
            - name: config-volume
              mountPath: /data/config
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "common.names.fullname" . }}-init-worker-config
            defaultMode: 0777

      restartPolicy: OnFailure
      terminationGracePeriodSeconds: 0

  ttlSecondsAfterFinished: 600
  backoffLimit: 3
  completions: 1
  parallelism: 1
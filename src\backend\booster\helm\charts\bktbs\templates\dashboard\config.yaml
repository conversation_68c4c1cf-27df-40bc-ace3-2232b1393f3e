{{- if .Values.dashboard.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.fullname" . }}-dashboard-config
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: dashboard
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
data:
  dashboard.json: |-
    {
      "v": {{ .Values.dashboard.log.verbose }},
      "alsologtostderr": {{ default "true" .Values.dashboard.log.alsoLogToStdErr }},
      "address": "0.0.0.0",
      "port": 30114,
      "disttask_mysql": {
        "disttask_enable":true,
        "disttask_mysql": "{{ include "bktbs.mariadb.address" . }}",
        "disttask_mysql_db": "{{ include "bktbs.mariadb.database" . }}",
        "disttask_mysql_user": "{{ include "bktbs.mariadb.username" . }}",
        "disttask_mysql_pwd": "{{ include "bktbs.mariadb.password" . }}"
      }
    }
{{- end }}

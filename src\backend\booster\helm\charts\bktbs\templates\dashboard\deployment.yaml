{{- if .Values.dashboard.enabled -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.fullname" . }}-dashboard
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: dashboard
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: dashboard
  replicas: 1
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: dashboard
        {{- if .Values.dashboard.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.podLabels "context" $) | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ template "bktbs.serviceAccountName" . }}
      {{- include "bktbs.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.dashboard.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.dashboard.podAffinityPreset "component" "dashboard" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.dashboard.podAntiAffinityPreset "component" "dashboard" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.dashboard.nodeAffinityPreset.type "key" .Values.dashboard.nodeAffinityPreset.key "values" .Values.dashboard.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.dashboard.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.priorityClassName }}
      priorityClassName: {{ .Values.dashboard.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.dashboard.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.dashboard.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      containers:
        - name: dashboard
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.dashboard.image "global" .Values.global) }}
          imagePullPolicy: {{ .Values.dashboard.image.pullPolicy }}
          {{- if .Values.dashboard.resources }}
          resources: {{- toYaml .Values.dashboard.resources | nindent 12 }}
          {{- end }}
          command: ["/data/workspace/start.sh"]
          args:
            - -f
            - /data/config/dashboard.json
          env:
            - name: BK_TBS_LOCAL_IP
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          ports:
            - name: http
              containerPort: 30114
              protocol: TCP
          volumeMounts:
            - name: config-volume
              mountPath: /data/config/
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "common.names.fullname" . }}-dashboard-config
{{- end }}
{"hooks": [{"src_command": "*/g++", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/gcc", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/clang++", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/clang", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/cc", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/c++", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/ld", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "*/lld", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "clang", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "g++", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "gcc", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "clang++", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "cc", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "c++", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "ld", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}, {"src_command": "lld", "target_command": "$LAUNCHER_HOOK $SRC_CMD"}]}
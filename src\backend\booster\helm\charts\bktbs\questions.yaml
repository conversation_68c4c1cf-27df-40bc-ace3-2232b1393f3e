questions:
  - variable: mariadb.enabled
    label: "是否部署mariadb"
    type: boolean
    default: true
    description: "通过Sub Charts方式部署mariadb服务"
    group: "mariadb配置"
  - variable: externalDatabase.host
    label: "外部mariadb服务host"
    type: string
    default: "localhost"
    description: "仅当mongodb.enabled=false时，该配置有效"
    group: "mariadb配置"
  - variable: externalDatabase.port
    label: "外部mariadb服务port"
    type: string
    default: "3306"
    description: "仅当mongodb.enabled=false时，该配置有效"
    group: "mariadb配置"
  - variable: externalDatabase.usernmae
    label: "外部mariadb服务usernmae"
    type: string
    default: ""
    description: "仅当mongodb.enabled=false时，该配置有效"
    group: "mariadb配置"
  - variable: externalDatabase.password
    label: "外部mariadb服务password"
    type: string
    default: ""
    description: "仅当mongodb.enabled=false时，该配置有效"
    group: "mariadb配置"
  - variable: externalDatabase.database
    label: "外部mariadb服务database"
    type: string
    default: ""
    description: "仅当mongodb.enabled=false时，该配置有效"
    group: "mariadb配置"
  - variable: ingress.enabled
    label: "是否创建ingress"
    type: boolean
    default: true
    description: "配置bkrepo Ingress，外部通过hostname方式访问"
    group: "Ingress配置"
    show_subquestion_if: true
    subquestions:
      - variable: nginx-ingress-controller.enabled
        label: "是否部署nginx-ingress-controller"
        type: boolean
        default: true
        description: "通过Sub Charts方式部署nginx-ingress-controller"
  - variable: server.host
    label: "bktbs hostname"
    type: string
    default: "bktbs.com"
    description: "bktbs必须通过hostname访问"
    group: "通用配置"
  - variable: server.enabled
    label: "是否部署server"
    type: boolean
    default: true
    group: "服务部署选择"
  - variable: gateway.enabled
    label: "是否部署gateway"
    type: boolean
    default: true
    group: "服务部署选择"
  - variable: dashboard.enabled
    label: "是否部署dashboard"
    type: boolean
    default: true
    group: "服务部署选择"
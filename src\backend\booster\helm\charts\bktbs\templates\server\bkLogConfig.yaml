{{- if .Values.server.enabled -}}
{{- if .Values.bkLogConfig.enable -}}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "common.names.fullname" . }}-server
spec:
  dataId: {{ .Values.bkLogConfig.server.dataId }}   # 对应数据上报管道
  logConfigType: std_log_config   # 采集类型，枚举值：container_log_config、node_log_config、std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:  
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: server
{{- end }}
{{- end }}
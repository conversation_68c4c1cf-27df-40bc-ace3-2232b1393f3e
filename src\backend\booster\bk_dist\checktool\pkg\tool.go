/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package pkg

import (
	"context"

	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/bk_dist/checktool/common"
	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/common/blog"
)

const (
	OSWindows = "windows"
)

// NewCheckTool get a new CheckTool
func NewCheckTool(flagsparam *common.Flags) *CheckTool {
	blog.Infof("CheckTool: new tool with flags:%+v", *flagsparam)

	return &CheckTool{
		flags: flagsparam,
	}
}

type CheckTool struct {
	flags *common.Flags
}

// Run run the tool
func (h *CheckTool) Run(ctx context.Context) (int, error) {
	return h.run(ctx)
}

func (h *CheckTool) run(pCtx context.Context) (int, error) {

	return 0, nil
}

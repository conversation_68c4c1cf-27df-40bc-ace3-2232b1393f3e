apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.fullname" . }}-init-worker-config
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: gateway
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
data:
  init.sh: |-
    #!/bin/bash
    worker_name={{ .Values.gateway.initWorkers.cc.name }}
    worker_image={{ .Values.gateway.initWorkers.cc.image }}
    gateway=http://{{ printf "%s-gateway:30113" (include "common.names.fullname" . ) }}
    if [[ -z "$worker_name" ]]; then
      echo "no worker should be init"
      exit 0
    fi

    while true; do
      code=$(curl -s -o /dev/null -w "%{http_code}" $gateway)
      if [[ "$code" == "404" ]]; then
        break
      fi
      sleep 1
    done

    curl -XPUT -d '{"data": {"image": "'$worker_image'"}, "operator": "admin"}' $gateway/api/v1/disttask-cc/resource/worker/$worker_name
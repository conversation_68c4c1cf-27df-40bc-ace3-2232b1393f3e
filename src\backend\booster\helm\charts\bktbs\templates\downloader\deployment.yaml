{{- if .Values.downloader.enabled -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.fullname" . }}-downloader
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: downloader
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: downloader
  replicas: 1
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: downloader
        {{- if .Values.downloader.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.downloader.podLabels "context" $) | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ template "bktbs.serviceAccountName" . }}
      {{- include "bktbs.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.downloader.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.downloader.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.downloader.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.downloader.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.downloader.podAffinityPreset "component" "downloader" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.downloader.podAntiAffinityPreset "component" "downloader" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.downloader.nodeAffinityPreset.type "key" .Values.downloader.nodeAffinityPreset.key "values" .Values.downloader.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.downloader.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.downloader.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.downloader.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.downloader.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.downloader.priorityClassName }}
      priorityClassName: {{ .Values.downloader.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.downloader.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.downloader.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      containers:
        - name: downloader
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.downloader.image "global" .Values.global) }}
          imagePullPolicy: {{ .Values.downloader.image.pullPolicy }}
          {{- if .Values.downloader.resources }}
          resources: {{- toYaml .Values.downloader.resources | nindent 12 }}
          {{- end }}
          env:
            - name: BK_TBS_LOCAL_IP
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: TBS_HOST
              value: {{ .Values.server.host }}:{{ .Values.server.servingPort }}
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
  {{- end }}
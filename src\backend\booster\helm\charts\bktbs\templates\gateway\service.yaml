{{- if .Values.gateway.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}-gateway
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: gateway
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
  type: {{ .Values.gateway.service.type }}
  {{- if eq .Values.gateway.service.type "LoadBalancer" }}
  {{- if .Values.gateway.service.loadBalancerIP }}
  loadBalancerIP: {{ default "" .Values.gateway.service.loadBalancerIP | quote }}
  {{- end }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.gateway.service.port }}
      {{- if and .Values.gateway.service.nodePort (or (eq .Values.gateway.service.type "NodePort") (eq .Values.gateway.service.type "LoadBalancer")) }}
      nodePort: {{ .Values.gateway.service.nodePort }}
      {{- else }}
      nodePort: null
      {{- end }}
      targetPort: http
{{- end }}

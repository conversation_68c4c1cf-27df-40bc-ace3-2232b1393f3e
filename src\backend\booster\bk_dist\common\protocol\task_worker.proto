package protocol;

enum PBCompressType {
	NONE = 0;
	LZO = 1;
	LZ4 = 2;
}

message PBFileDesc {
    required string fullpath = 1;
	required int64 size = 2; 
	required string md5 = 3;
	required PBCompressType compresstype = 4;
	required int64 compressedsize = 5; 
	optional bytes buffer = 6;
	// to specified relative path in target 
	optional string targetrelativepath = 7;
	optional uint32 filemode = 8;
	optional bytes linktarget = 9;
	optional int64 modifytime = 10;
	optional int64 accesstime = 11;
	optional int64 createtime = 12;
}

message PBFileResult {
    required string fullpath = 1;
	required int32 retcode = 2;
	optional string targetrelativepath = 3;
}

message PBCommand {
    required string workdir = 1;
	required string exepath = 2;
	required string exename = 3;
	repeated string params = 4;
	repeated PBFileDesc inputfiles = 5;
	repeated string resultfiles = 6;
	repeated bytes env = 7;
}

message PBStatEntry {
	required string key = 1;
	required int64 time = 2;
}

message PBResult {
	required PBCommand cmd = 1;
	required int32 retcode = 2;
	required string outputmessage = 3;
	required string errormessage = 4;
	repeated PBFileDesc resultfiles = 5;
	repeated PBStatEntry stats = 6;
}

message PBCacheParam {
    required bytes name = 1;
    required bytes md5 = 2;
    optional int64 size = 3;
    required bytes target = 4;
    optional int32 overwrite = 5;
	optional uint32 filemode = 6;
	optional bytes linktarget = 7;
	optional int64 modifytime = 8;
	optional int64 accesstime = 9;
	optional int64 createtime = 10;
}

enum PBCacheStatus {
    NOFOUND = 0;
    SUCCESS = 1;
    ERRORWHILEFINDING = 2;
    ERRORWHILESAVING = 3;
}

message PBCacheResult {
    required PBCacheStatus status = 1;
    required bytes reason = 2;
}

enum PBCmdType {
    DISPATCHTASKREQ = 0;
    DISPATCHTASKRSP = 1;
	SYNCTIMEREQ = 2;
    SYNCTIMERSP = 3;
	SENDFILEREQ = 4;
    SENDFILERSP = 5;
    CHECKCACHEREQ = 6;
    CHECKCACHERSP = 7;
	REPORTRESULTCACHEREQ = 8;
	REPORTRESULTCACHERSP = 9;
	QUERYRESULTCACHEINDEXREQ = 10;
	QUERYRESULTCACHEINDEXRSP = 11;
	QUERYRESULTCACHEFILEREQ = 12;
	QUERYRESULTCACHEFILERSP = 13;
	QUERYSLOTREQ = 20;
	QUERYSLOTRSP = 21;
	SLOTRSPACK = 22;
	LONGTCPHANDSHAKEREQ = 100;
	UNKNOWN = 9999;
}

message PBHead {
	required string version = 1;
	required string magic = 2;
	required int32 bodylen = 3;
	required int64 buflen = 4;
	required PBCmdType cmdtype = 5;
	optional string business = 6;
	optional string taskid = 7;
}

message PBBodyDispatchTaskReq {
	repeated PBCommand cmds = 1;
}

message PBBodyDispatchTaskRsp {
	repeated PBResult results = 1;
}

message PBBodySyncTimeRsp {
	required int64 timenanosecond = 1;
}

message PBBodySendFileReq {
	repeated PBFileDesc inputfiles = 1;
}

message PBBodySendFileRsp {
	repeated PBFileResult results = 1;
}

message PBBodyCheckCacheReq {
    repeated PBCacheParam params = 1;
}

message PBBodyCheckCacheRsp {
    repeated PBCacheResult results = 1;
}

message PBBodyQuerySlotReq {
	required int32 Priority = 1;
	required int32 waittotaltasknum = 2;
	required string tasktype = 3;
}

message PBBodyQuerySlotRsp {
	required int32 availableslotnum = 1;
	required int32 refused = 2;
	required string message = 3;
}

message PBBodySlotRspAck {
	required int32 consumeslotnum = 1;
}

message PBAttributesEntry {
	required string key = 1;
	required string value = 2;
}

message PBAttributesEntryArray {
	repeated PBAttributesEntry array = 1;
}

message PBBodyReportResultCacheReq {
    repeated PBAttributesEntry attributes = 1;   // 标识结果的多个属性，考虑带上version，方便扩展
	repeated PBFileDesc resultfiles = 2;         // repeated意味着0次或者多次
}

message PBBodyReportResultCacheRsp {
    required int32 retcode = 1;
	required string outputmessage = 2;
	required string errormessage = 3;
}

message PBBodyQueryResultCacheIndexReq {
    repeated PBAttributesEntry attributes = 1;   // 获取列表
}

message PBBodyQueryResultCacheIndexRsp {
	optional bytes list = 1;   // 列表，可以为空
	optional PBCompressType compresstype = 2;
	optional int32 originlength = 3;
}

message PBBodyQueryResultCacheFileReq {
    repeated PBAttributesEntry attributes = 1;   // 获取key的结果
}

message PBBodyQueryResultCacheFileRsp {
    repeated PBFileDesc resultfiles = 1;         // 指定key的结果
}
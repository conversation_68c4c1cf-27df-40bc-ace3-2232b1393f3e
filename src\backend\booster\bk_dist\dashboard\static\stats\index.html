<!DOCTYPE html>
<html>

<base id="base" href="" />
<script>
    let api_prefix = "";
    let prefix = window.location.pathname;
    if (prefix && prefix[0] == "/") {
        prefix = prefix.substring(1, prefix.length)
        if (prefix[prefix.length-1] != "/") {
            document.getElementById("base").href = prefix + "/";
        }
        if (prefix) {
            api_prefix = "/" + prefix;
        }
    }

</script>
<head>
    <title>编译加速数据分析</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="css/font-awesome.css" rel="stylesheet">
    <link href="css/bootstrap_noresponsive.css" rel="stylesheet">
    <link href="css/bk.css" rel="stylesheet">
    <link rel="stylesheet" href="css/bootstrap-admin-theme.css">

    <link rel="stylesheet" href="css/index.css">
    <link rel="icon" type="image/png" href="img/icon.png">
    <link href="css/toastr.min.css" rel="stylesheet">
    <script type="text/javascript" src="js/vue.min.js"></script>
    <!-- 以下两个插件用于在IE8以及以下版本浏览器支持HTML5元素和媒体查询，如果不需要用可以移除 -->
    <!--[if lt IE 9]>
    <script src="js/html5shiv.min.js"></script>
    <script src="js/respond.min.js"></script>
    <![endif]-->
</head>

<body class="bootstrap-admin-with-small-navbar">
<!-- main / large navbar -->
<nav class="navbar navbar-default navbar-fixed-top bootstrap-admin-navbar bootstrap-admin-navbar-under-small"
     role="navigation">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse"
                            data-target=".main-navbar-collapse">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="javascript:;">Build-Booster Analysis Dashboard 编译加速数据分析</a>
                </div>
                <!-- /.navbar-collapse -->
            </div>
        </div>
    </div>
    <!-- /.container -->
</nav>

<div class="container">
    <!-- left, vertical navbar & content -->
    <div class="row">
        <!-- content -->
        <div class="col-md-12">

            <div class="row">
                <div class="col-lg-12">
                    <div class="alert alert-success bootstrap-admin-alert">
                        <button type="button" class="close" data-dismiss="alert">×</button>
                        <h4>欢迎使用编译加速数据分析</h4>
                    </div>
                </div>
            </div>

            <div class="data-holder">
                <div class="row mb10">
                    <div class="col-lg-12">
                        <a class="king-btn-demo king-btn king-radius king-success ml5" onclick="shareDetailLink()"
                           style="float: right">
                            <i class="fa fa-mail-reply-all btn-icon"></i>分享详情
                        </a>
                        <a class="king-btn-demo king-btn king-radius king-primary" onclick="shareListLink()"
                           style="float: right">
                            <i class="fa fa-mail-reply-all btn-icon"></i>分享列表
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="form-group">
                            <div>
                                <input type="text" class="form-control" id="input_data"
                                       placeholder="输入TaskID或WorkID 回车开始分析" v-on:keyup.enter="start">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-1">
                        <div class="form-group">
                            <select id="input_scene" class="form-control">
                                <option value="cc">CC</option>
                                <option value="ue4">UE4</option>
                                <option value="clang-cl">clang-cl</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1">
                        <div class="form-group">
                            <select id="input_day" class="form-control">
                                <option value="1">一天内</option>
                                <option value="3">三天内</option>
                                <option value="5">五天内</option>
                                <option value="7">七天内</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1">
                        <div class="form-group">
                            <select id="input_type" class="form-control">
                                <option value="TASK">TASK</option>
                                <option value="WORK">WORK</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1">
                        <div class="form-group">
                            <input type="text" class="form-control" id="input_user"
                                   placeholder="user" v-on:keyup.enter="start_list">
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <div class="form-group">
                            <input type="text" class="form-control" id="input_client_ip"
                                   placeholder="client_ip" v-on:keyup.enter="start_list">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div>
                                <input type="text" class="form-control" id="input_project"
                                       placeholder="输入加速方案 回车开始分析" v-on:keyup.enter="start_list">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12" id="work-table-holder" style="display: none">
                        <table id="work_table" class="table table-bordered table hover">
                            <thead>
                            <tr>
                                <td>WorkID</td>
                                <td>发起时间</td>
                                <td>耗时</td>
                                <td>结果</td>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="col-md-12" id="task-table-holder" style="display: none">
                        <table id="task_table" class="table table-bordered table hover">
                            <thead>
                            <tr>
                                <td>TaskID</td>
                                <td>发起时间</td>
                                <td>耗时</td>
                                <td>用户</td>
                                <td>IP</td>
                                <td>结果</td>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <div class="text-muted bootstrap-admin-box-title">基础信息</div>
                                <div class="pull-right"></div>
                            </div>
                            <div class="bootstrap-admin-panel-content" style="height: 850px;">
                                <section class="panel panel-box">
                                    <div class="list-justified-container" v-cloak style="padding:55px 0">
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f20">{{ work_result }}</p>
                                                <p class="text-muted">编译结果</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ total_time }}</p>
                                                <p class="text-muted">总时长</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ apply_time }}</p>
                                                <p class="text-muted">申请资源</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ real_time }}</p>
                                                <p class="text-muted">实际编译</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f20">{{ work_data.job_remote_ok }}</p>
                                                <p class="text-muted">远程成功任务</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ work_data.job_remote_error }}</p>
                                                <p class="text-muted">远程失败任务</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ work_data.job_local_ok }}</p>
                                                <p class="text-muted">本地成功任务</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ work_data.job_local_error }}</p>
                                                <p class="text-muted">本地失败任务</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f20">{{ remote_compile_err_count }}</p>
                                                <p class="text-muted">远程编译失败</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ remote_fatal_without_timeout_count }}</p>
                                                <p class="text-muted">远程异常中断</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ remote_fatal_timeout_count }}</p>
                                                <p class="text-muted">远程超时中断</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ remote_timeout }}s</p>
                                                <p class="text-muted">远程超时阈值</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f20">{{ ccache_direct_hit }}</p>
                                                <p class="text-muted">ccache直接hit</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ ccache_preprocessed_hit }}</p>
                                                <p class="text-muted">ccache预处理hit</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ ccache_miss }}</p>
                                                <p class="text-muted">ccache miss</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ ccache_hit_rate }}</p>
                                                <p class="text-muted">cache hit rate</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f20">{{ tbs_direct_hit_var }}</p>
                                                <p class="text-muted">tbs直接hit</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ tbs_preprocess_hit_var }}</p>
                                                <p class="text-muted">tbs预处理hit</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ tbs_miss_var }}</p>
                                                <p class="text-muted">tbs miss</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ tbs_hit_rate_var }}</p>
                                                <p class="text-muted">tbs hit rate</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f20">{{ task_data.client_cpu }}</p>
                                                <p class="text-muted">本地CPU</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ task_data.cpu_total }}</p>
                                                <p class="text-muted">远程CPU</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ task_data.queue_name }}</p>
                                                <p class="text-muted">加速集群地区</p>
                                            </li>
                                            <li>
                                                <p class="f20">{{ task_data.source_ip }}</p>
                                                <p class="text-muted">发起机器IP</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li style="width: 240px !important;">
                                                <p class="f20">{{ task_data.client_version }}</p>
                                                <p class="text-muted">client版本</p>
                                            </li>
                                            <li style="width: 240px !important;">
                                                <p class="f20">{{ task_data.worker_version }}</p>
                                                <p class="text-muted">worker版本</p>
                                            </li>
                                        </ul>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <div class="text-muted bootstrap-admin-box-title">统计信息</div>
                                <div class="pull-right"></div>
                            </div>
                            <div class="bootstrap-admin-panel-content" style="height: 850px;">
                                <section class="panel panel-box">
                                    <div class="list-justified-container" v-cloak style="padding:30px 0">
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f15">{{ pre_work_lock_avg_time }}</p>
                                                <p class="text-muted">预处理持锁<br>平均等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ pre_work_lock_longest_time }}</p>
                                                <p class="text-muted">预处理持锁<br>最长等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ pre_work_avg_time }}</p>
                                                <p class="text-muted">预处理<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ pre_work_longest_time }}</p>
                                                <p class="text-muted">预处理<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f15">{{ remote_work_lock_avg_time }}</p>
                                                <p class="text-muted">远程持锁<br>平均等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_lock_longest_time }}</p>
                                                <p class="text-muted">远程持锁<br>最长等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_process_avg_time }}</p>
                                                <p class="text-muted">远程处理<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_process_longest_time }}</p>
                                                <p class="text-muted">远程处理<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f15">{{ local_work_lock_avg_time }}</p>
                                                <p class="text-muted">本地持锁<br>平均等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ local_work_lock_longest_time }}</p>
                                                <p class="text-muted">本地持锁<br>最长等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ local_work_avg_time }}</p>
                                                <p class="text-muted">本地处理<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ local_work_longest_time }}</p>
                                                <p class="text-muted">本地处理<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center">
                                            <li>
                                                <p class="f15">{{ post_work_lock_avg_time }}</p>
                                                <p class="text-muted">后置处理持锁<br>平均等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ post_work_lock_longest_time }}</p>
                                                <p class="text-muted">后置处理持锁<br>最长等待时间</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ post_work_avg_time }}</p>
                                                <p class="text-muted">后置处理<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ post_work_longest_time }}</p>
                                                <p class="text-muted">后置处理<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f15">{{ remote_work_pack_common_avg_time }}</p>
                                                <p class="text-muted">文件压缩<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_pack_common_longest_time }}</p>
                                                <p class="text-muted">文件压缩<br>最长耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_send_common_avg_time }}</p>
                                                <p class="text-muted">文件分发<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_send_common_longest_time }}</p>
                                                <p class="text-muted">文件分发<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f15">{{ remote_work_pack_avg_time }}</p>
                                                <p class="text-muted">命令压缩<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_pack_longest_time }}</p>
                                                <p class="text-muted">命令压缩<br>最长耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_send_avg_time }}</p>
                                                <p class="text-muted">命令分发<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_send_longest_time }}</p>
                                                <p class="text-muted">命令分发<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f15">{{ remote_work_receive_avg_time }}</p>
                                                <p class="text-muted">结果接收<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_receive_longest_time }}</p>
                                                <p class="text-muted">结果接收<br>最长耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_unpack_avg_time }}</p>
                                                <p class="text-muted">结果解压<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_unpack_longest_time }}</p>
                                                <p class="text-muted">结果解压<br>最长耗时</p>
                                            </li>
                                        </ul>
                                        <ul class="list-justified text-center" style="padding-top: 10px;">
                                            <li>
                                                <p class="f15">{{ remote_work_wait_avg_time }}</p>
                                                <p class="text-muted">远程处理排队<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_work_wait_longest_time }}</p>
                                                <p class="text-muted">远程处理排队<br>最长耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_error_avg_time }}</p>
                                                <p class="text-muted">远程异常中断<br>平均耗时</p>
                                            </li>
                                            <li>
                                                <p class="f15">{{ remote_error_longest_time }}</p>
                                                <p class="text-muted">远程异常中断<br>最长耗时</p>
                                            </li>
                                        </ul>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">并发数据曲线(瞬时)</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="concurrency_chart_instant"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">并发数据曲线(峰值)</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="concurrency_chart" style="padding:10px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">任务等待队列曲线(瞬时)</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="waiting_chart_instant" style="padding:10px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">任务等待队列曲线(峰值)</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="waiting_chart" style="padding:10px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">任务处理曲线</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="sum_chart" style="padding:10px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">任务处理耗时分布</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="process_chart" style="padding:10px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="text-muted bootstrap-admin-box-title">命令耗时分布</div>
                        </div>
                        <div class="bootstrap-admin-panel-content" style="height:400px">
                            <canvas id="command_chart" style="padding:10px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12">
                <button class="left-button king-btn-demo king-btn king-radius king-primary" onclick="CreateReporter()" title="下载详细数据">
                    <i class="fa fa-search btn-icon"></i>下载详细数据
                </button>
                <button class="right-button king-btn-demo king-btn king-radius king-primary" onclick="ShowOrHideRemoteErrors()" title="远程错误">
                    <i class="fa btn-icon"></i>远程错误
                </button>
            </div>

            <div id="divider_before_table" class="divider col-md-12 display hidden"></div>

            <table id="remote_error_table" class="col-md-12 display hidden" style="width:100%;border-collapse: separate; border-spacing: 10px;">
                <thead>
                    <tr class="panel-heading">
                        <th class="light-red">远程机器</th>
                        <th class="light-red">错误信息</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>

        </div>
    </div>
    <textarea id="js-copytextarea" style="visibility: hidden"></textarea>
</div>
<div class="king-loading" id="loding" style="display: none;">
    <img src="img/loading_1_b_36x36.gif"/>
</div>
<script src="js/jquery-1.10.2.min.js"></script>
<script src="js/toastr.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/twitter-bootstrap-hover-dropdown.min.js"></script>
<script type="text/javascript" src="js/pako.js"></script>
<script type="text/javascript" src="js/index.js"></script>
<script src="js/chart.min.js"></script>
<script src="js/bk.js"></script>
<script src="js/jquery.dataTables.js" type="text/javascript"></script>
<script src="js/dataTables.bootstrap.js" type="text/javascript"></script>
</body>

</html>
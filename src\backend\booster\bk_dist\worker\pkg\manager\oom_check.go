/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package manager

import (
	"time"

	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/common/blog"
	"github.com/shirou/gopsutil/mem"
)

/*
* 考虑优先选择执行时间短，内存占用超过一定阈值的子进程
*
* 触发条件：
*	默认5秒检查1次，当系统内存占用超过85%，或者剩余内存不到2G，每秒检查1次
*	当系统剩余内存不到1G时，触发kill(直到小于kill条件为止)
 */

const (
	// busyTotalMemoryPercent   = 80.00                  // 系统使用的内存超过该值触发busy
	// busyTotalMemoryAvailable = 2 * 1024 * 1024 * 1024 //2GB 系统可用内存少于该值触发busy
	// killTotalMemoryAvailable = 1 * 1024 * 1024 * 1024 //1GB 系统可用内存少于该值触发kill

	// 测试条件
	busyTotalMemoryPercent   = 40.00                  // 系统使用的内存超过该值触发busy
	busyTotalMemoryAvailable = 8 * 1024 * 1024 * 1024 //2GB 系统可用内存少于该值触发busy
	killTotalMemoryAvailable = 8 * 1024 * 1024 * 1024 //1GB 系统可用内存少于该值触发kill

	freeCheckInterval = 5 * time.Second
	busyCheckInterval = 1 * time.Second
)

func getTotalMemory() *mem.VirtualMemoryStat {
	v, err := mem.VirtualMemory()
	if err != nil {
		blog.Warnf("get total memory failed with error:%v", err)
		return nil
	}

	return v
}

func isBusy(v *mem.VirtualMemoryStat) bool {
	if v != nil && (v.Available < busyTotalMemoryAvailable || v.UsedPercent > busyTotalMemoryPercent) {
		return true
	}

	return false
}

func needKill(v *mem.VirtualMemoryStat) bool {
	if v != nil && v.Available < killTotalMemoryAvailable {
		return true
	}

	return false
}

func checkTotalMemory() (busy, kill bool) {
	blog.Debugf("check total memory in...")

	v := getTotalMemory()
	if v == nil {
		return
	}
	blog.Debugf("check total memory got memory info:%+v", *v)

	if isBusy(v) {
		busy = true
	}

	if needKill(v) {
		kill = true
	}

	blog.Infof("check total memory, Available %d UsedPercent %f, busy: %v kill: %v",
		v.Available, v.UsedPercent, busy, kill)
	return
}

func doKill() {
	blog.Infof("do kill now...")

	killOneChild()

	for needKill(getTotalMemory()) {
		// TODO : do kill now
		killOneChild()
	}
}

func oomcheck() {
	blog.Infof("start oom check goroutine now...")

	d := freeCheckInterval
	timer := time.NewTimer(d)

	// 先执行一次 getChildren()，方便加快系统该命令的响应
	getChildren()
	// 后续每5次空闲循序触发该函数一次
	freeCounter := 0

	for {
		<-timer.C
		// change by memory status
		busy, kill := checkTotalMemory()
		if busy {
			d = busyCheckInterval
			freeCounter = 0
		} else {
			d = freeCheckInterval
			freeCounter++
		}

		if freeCounter > 0 && freeCounter%5 == 0 {
			getChildren()
		}

		if kill {
			doKill()
		}

		timer.Reset(d)
	}
}

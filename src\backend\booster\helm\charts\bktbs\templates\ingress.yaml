{{- if .Values.ingress.enabled -}}
apiVersion: {{ include "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ include "common.names.fullname" . }}-ingress
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
      {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.ingress.annotations }}
    {{ $key }}: {{ include "common.tplvalues.render" (dict "value" $value "context" $) | quote }}
    {{- end }}
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Real-Ip $remote_addr";
spec:
  rules:
    - host: {{ .Values.server.host }}
      http:
        paths:
          - path: /server(/|$)(.*)
            {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
            pathType: Prefix
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (printf "%s-server" (include "common.names.fullname" .)) "servicePort" "http" "context" $)  | nindent 14 }}
    - host: {{ .Values.server.host }}
      http:
        paths:
          - path: /gateway(/|$)(.*)
            {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
            pathType: Prefix
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (printf "%s-gateway" (include "common.names.fullname" .)) "servicePort" "http" "context" $)  | nindent 14 }}
    - host: {{ .Values.server.host }}
      http:
        paths:
          - path: /dashboard(/|$)(.*)
            {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
            pathType: Prefix
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (printf "%s-dashboard" (include "common.names.fullname" .)) "servicePort" "http" "context" $)  | nindent 14 }}
    - host: {{ .Values.server.host }}
      http:
        paths:
          - path: /downloads(/|$)(.*)
            {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
            pathType: Prefix
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (printf "%s-downloader" (include "common.names.fullname" .)) "servicePort" "http" "context" $)  | nindent 14 }}
  {{- end }}

# Create a new CROSSTOOL file for our toolchain.
build:bk_distcc --crosstool_top=//@BK_TOOLCHAIN_DIR:distcccompile

# Use --cpu as a differentiator.
build:bk_distcc --cpu=cpudistcc

# Specify a "sane" C++ toolchain for the host platform.
build:bk_distcc --host_crosstool_top=@bazel_tools//tools/cpp:toolchain
build:bk_distcc --jobs=@BK_JOBS
build:bk_distcc --local_resources 102400,102400,1

# env variables
build:bk_distcc --action_env=DISTCC_DIR="@BK_DISTCC_DIR"
build --action_env=HOME

# set config
build --config=bk_distcc


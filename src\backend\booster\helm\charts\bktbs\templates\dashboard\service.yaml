{{- if .Values.dashboard.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}-dashboard
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: dashboard
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: dashboard
  type: {{ .Values.dashboard.service.type }}
  {{- if eq .Values.dashboard.service.type "LoadBalancer" }}
  {{- if .Values.dashboard.service.loadBalancerIP }}
  loadBalancerIP: {{ default "" .Values.dashboard.service.loadBalancerIP | quote }}
  {{- end }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.dashboard.service.port }}
      {{- if and .Values.dashboard.service.nodePort (or (eq .Values.dashboard.service.type "NodePort") (eq .Values.dashboard.service.type "LoadBalancer")) }}
      nodePort: {{ .Values.dashboard.service.nodePort }}
      {{- else }}
      nodePort: null
      {{- end }}
      targetPort: http
{{- end }}

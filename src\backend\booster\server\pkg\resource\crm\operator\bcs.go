/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package operator

import (
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/common/blog"
	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/common/metric/controllers"
	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/server/config"
	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/server/pkg/engine"
	selfMetric "github.com/TencentBlueKing/bk-turbo/src/backend/booster/server/pkg/metric"
	rsc "github.com/TencentBlueKing/bk-turbo/src/backend/booster/server/pkg/resource"
)

// Operator define a bcs handler to do all operations.
type Operator interface {
	GetResource(clusterID string) ([]*NodeInfo, error)
	GetServerStatus(clusterID, namespace, name string) (*ServiceInfo, error)
	LaunchServer(clusterID string, param BcsLaunchParam) error
	ScaleServer(clusterID string, namespace, name string, instance int) error
	ReleaseServer(clusterID, namespace, name string) error
}

// BcsLaunchParam describe the launch request param through bcs
// including mesos/k8s/devcloud-mac
type BcsLaunchParam struct {
	// application name
	Name string

	// application namespace
	Namespace string

	// attribute condition, matters the constraint strategy
	AttributeCondition map[string]string

	// env key-values which will be inserted into containers
	Env map[string]string

	// ports that implements with port_name:protocol
	// such as my_port_alpha:http, my_port_beta:tcp
	// port numbers are all generated by container scheduler with cnm HOST
	Ports map[string]string

	// volumes implements the hostPath volumes with name:settings
	Volumes map[string]BcsVolume

	// container images
	Image string

	// instance number to launch
	Instance int
}

// BcsVolume describe the volume mapping settings
type BcsVolume struct {
	ContainerDir string
	HostDir      string
}

const (
	AttributeKeyCity     = "City"
	AttributeKeyPlatform = "Platform"
)

// CheckQueueKey describe the function that get queue key from attributes
func (param *BcsLaunchParam) CheckQueueKey(instanceType config.InstanceType) bool {
	platform, city := getInstanceKey(param.AttributeCondition)
	if instanceType.Group == city && instanceType.Platform == platform {
		return true
	}
	return false
}

// InstanceFilterFunction describe the function that decide how many instance to launch/scale.
type InstanceFilterFunction func(availableInstance int) (int, error)

// NodeInfo 描述了从各个operator处获取的集群单个节点信息,
// 用于获取节点的标签, 资源使用情况等
type NodeInfo struct {
	IP         string
	Hostname   string
	DiskTotal  float64
	MemTotal   float64
	CPUTotal   float64
	DiskUsed   float64
	MemUsed    float64
	CPUUsed    float64
	DiskLeft   float64
	MemLeft    float64
	CPULeft    float64
	Attributes map[string]string

	Disabled bool
}

// FigureAvailableInstanceFromFree 根据节点剩余资源计算可分配实例数
func (ni *NodeInfo) FigureAvailableInstanceFromFree(cpuPerInstance, memPerInstance, diskPerInstance float64) int {
	if cpuPerInstance == 0 || memPerInstance == 0 || diskPerInstance == 0 {
		return 0
	}
	var instanceByCPU, instanceByMem, instanceByDisk float64
	if ni.CPULeft > 0.0 {
		instanceByCPU = ni.CPULeft / cpuPerInstance
	} else {
		instanceByCPU = (ni.CPUTotal - ni.CPUUsed) / cpuPerInstance
	}
	if ni.MemLeft > 0.0 {
		instanceByMem = ni.MemLeft / memPerInstance
	} else {
		instanceByMem = (ni.MemTotal - ni.MemUsed) / memPerInstance
	}
	if ni.DiskLeft > 0.0 {
		instanceByDisk = ni.DiskLeft / diskPerInstance
	} else {
		instanceByDisk = (ni.DiskTotal - ni.DiskUsed) / diskPerInstance
	}

	return int(math.Min(math.Min(instanceByCPU, instanceByMem), instanceByDisk))
}

func (ni *NodeInfo) valid() bool {
	return ni.CPUTotal >= 0 && ni.MemTotal >= 0 && ni.DiskTotal >= 0
}

type noReadyInfoDetail struct {
	caller string
	start  time.Time
	num    int32
}

type noReadyInfoBlock struct {
	blockkey string
	total    int32
	details  map[string]*noReadyInfoDetail
}

var (
	noReadyInfoLock sync.RWMutex
	noReadyInfo     map[string]*noReadyInfoBlock = make(map[string]*noReadyInfoBlock, 10)
)

func addNoReadyInfo(blockkey, caller string, num int32) {
	noReadyInfoLock.Lock()
	defer noReadyInfoLock.Unlock()

	block, ok := noReadyInfo[blockkey]
	var detail *noReadyInfoDetail
	var ok1 bool
	if ok {
		// 如果block已存在，更新或删除detail信息
		detail, ok1 = block.details[caller]
		if ok1 {
			detail.num += num
			block.total += num

			if detail.num == 0 {
				delete(block.details, caller)
			}
		} else {
			// 如果num为负数且caller不存在，记录错误并返回
			if num <= 0 {
				blog.Errorf("bcs: block(%s) caller(%s) noready num(%d) less than 0, do nothing", blockkey, caller, num)
				return
			}
			// 创建新的detail并添加到block中
			detail = &noReadyInfoDetail{
				caller: caller,
				start:  time.Now(),
				num:    num,
			}
			block.details[caller] = detail
			block.total += num
		}
	} else {
		// 如果block不存在且num为负数，记录错误并返回
		if num <= 0 {
			blog.Errorf("bcs: block(%s) caller(%s) noready num(%d) less than 0, do nothing", blockkey, caller, num)
			return
		}

		// 如果block不存在且num>0，创建新的block和detail
		detail = &noReadyInfoDetail{
			caller: caller,
			start:  time.Now(),
			num:    num,
		}
		block = &noReadyInfoBlock{
			blockkey: blockkey,
			total:    0,
			details:  make(map[string]*noReadyInfoDetail, 10),
		}
		block.details[caller] = detail
		block.total += num
		noReadyInfo[blockkey] = block
	}

	blog.Infof("bcs: block(%s) total noready:%d after add %d by caller(%s)",
		blockkey, block.total, num, caller)
}

// PrintNoReadyInfo print all no ready info
func PrintNoReadyInfo() {
	noReadyInfoLock.RLock()
	defer noReadyInfoLock.RUnlock()

	allinfo := ""
	for _, v := range noReadyInfo {
		allinfo += fmt.Sprintf("%s %d:[", v.blockkey, v.total)
		for _, d := range v.details {
			if d.num == 0 {
				continue
			}
			allinfo += fmt.Sprintf("%s:%d %v | ", d.caller, d.num, d.start)
		}
		allinfo += "];"
	}

	blog.Infof("bcs: all noready info: %s", allinfo)
}

// NewNodeInfoPool get a new node info pool
func NewNodeInfoPool(conf *config.ContainerResourceConfig) *NodeInfoPool {
	nip := NodeInfoPool{
		cpuPerInstance:       conf.BcsCPUPerInstance,
		memPerInstance:       conf.BcsMemPerInstance,
		cpuPerInstanceOffset: conf.BcsCPUPerInstanceOffset,
		memPerInstanceOffset: conf.BcsMemPerInstanceOffset,
		diskPerInstance:      1,
		nodeBlockMap:         make(map[string]*NodeInfoBlock, 1000),
	}
	for _, istItem := range conf.InstanceType {
		condition := map[string]string{
			AttributeKeyCity:     istItem.Group,
			AttributeKeyPlatform: istItem.Platform,
		}
		key := getBlockKey(condition)
		newBlock := NodeInfoBlock{
			CPUPerInstance:       conf.BcsCPUPerInstance,
			MemPerInstance:       conf.BcsMemPerInstance,
			CPUPerInstanceOffset: conf.BcsCPUPerInstanceOffset,
			MemPerInstanceOffset: conf.BcsMemPerInstanceOffset,
		}
		if istItem.CPUPerInstance > 0.0 {
			newBlock.CPUPerInstance = istItem.CPUPerInstance
		}
		if istItem.MemPerInstance > 0.0 {
			newBlock.MemPerInstance = istItem.MemPerInstance
		}
		if istItem.CPUPerInstanceOffset > 0.0 && istItem.CPUPerInstanceOffset < istItem.CPUPerInstance {
			newBlock.CPUPerInstanceOffset = istItem.CPUPerInstanceOffset
		}
		if istItem.MemPerInstanceOffset > 0.0 && istItem.MemPerInstanceOffset < istItem.MemPerInstance {
			newBlock.MemPerInstanceOffset = istItem.MemPerInstanceOffset
		}
		nip.nodeBlockMap[key] = &newBlock
	}
	return &nip
}

// NodeInfoPool 描述了一个节点集合的资源情况, 一般用来管理整个集群的资源情况
type NodeInfoPool struct {
	sync.Mutex

	cpuPerInstance       float64
	memPerInstance       float64
	cpuPerInstanceOffset float64
	memPerInstanceOffset float64
	diskPerInstance      float64
	lastUpdateTime       time.Time

	nodeBlockMap map[string]*NodeInfoBlock
}

// RecoverNoReadyBlock 对给定区域key的资源, 加上noReady个未就绪标记
// 一般用于在系统恢复时, 从数据库同步之前未就绪的数据信息
func (nip *NodeInfoPool) RecoverNoReadyBlock(key string, noReady int, caller string) {
	if _, ok := nip.nodeBlockMap[key]; !ok {
		nip.nodeBlockMap[key] = &NodeInfoBlock{}
	}

	nip.nodeBlockMap[key].noReadyInstance += noReady

	addNoReadyInfo(key, caller, int32(noReady))
}

// GetStats get status message
func (nip *NodeInfoPool) GetStats() string {
	nip.Lock()
	defer nip.Unlock()

	message := ""
	for city, block := range nip.nodeBlockMap {
		var cpuLeftStr, memLeftStr string
		if block.CPULeft > 0.0 {
			cpuLeftStr = fmt.Sprintf("%.2f", block.CPULeft)
		} else {
			cpuLeftStr = fmt.Sprintf("%.2f/%.2f", block.CPUTotal-block.CPUUsed,
				block.CPUTotal)
		}
		if block.MemLeft > 0.0 {
			memLeftStr = fmt.Sprintf("%.2f", block.MemLeft)
		} else {
			memLeftStr = fmt.Sprintf("%.2f/%.2f", block.MemTotal-block.MemUsed,
				block.MemTotal)
		}

		message += fmt.Sprintf(
			"\nCity: %s[cpuPerInstance:%.2f,cpuPerIstOffset:%.2f,memPerInstance:%.2f,memPerIstOffset:%.2f], available-instance: %d, report-instance: %d, noready-instance: %d "+
				"CPU-Left: %s, MEM-Left: %s",
			city,
			block.CPUPerInstance,
			block.CPUPerInstanceOffset,
			block.MemPerInstance,
			block.MemPerInstanceOffset,
			block.AvailableInstance-block.noReadyInstance,
			block.AvailableInstance, block.noReadyInstance,
			cpuLeftStr,
			memLeftStr,
		)
	}

	return message
}

// GetDetail get detail data
func (nip *NodeInfoPool) GetDetail() []*rsc.RscDetails {
	nip.Lock()
	defer nip.Unlock()

	r := make([]*rsc.RscDetails, 0, len(nip.nodeBlockMap))
	for city, block := range nip.nodeBlockMap {
		r = append(r, &rsc.RscDetails{
			Labels:            city,
			CPUTotal:          block.CPUTotal,
			CPUUsed:           block.CPUUsed,
			MemTotal:          block.MemTotal,
			MemUsed:           block.MemUsed,
			CPUPerInstance:    nip.cpuPerInstance,
			AvailableInstance: block.AvailableInstance - block.noReadyInstance,
			ReportInstance:    block.AvailableInstance,
			NotReadyInstance:  block.noReadyInstance,
		})
	}

	return r
}

// GetLastUpdateTime 获取上次资源数据更新的时间
func (nip *NodeInfoPool) GetLastUpdateTime() time.Time {
	nip.Lock()
	defer nip.Unlock()

	return nip.lastUpdateTime
}

func (nip *NodeInfoPool) getNodeInstance(key string) (float64, float64, float64, float64) {
	cpuPerInstance := nip.cpuPerInstance
	memPerInstance := nip.memPerInstance
	cpuPerInstanceOffset := nip.cpuPerInstanceOffset
	memPerInstanceOffset := nip.memPerInstanceOffset
	if _, ok := nip.nodeBlockMap[key]; ok {
		if nip.nodeBlockMap[key].CPUPerInstance > 0.0 {
			cpuPerInstance = nip.nodeBlockMap[key].CPUPerInstance
		}
		if nip.nodeBlockMap[key].MemPerInstance > 0.0 {
			memPerInstance = nip.nodeBlockMap[key].MemPerInstance
		}
		if nip.nodeBlockMap[key].CPUPerInstanceOffset > 0.0 {
			cpuPerInstanceOffset = nip.nodeBlockMap[key].CPUPerInstanceOffset
		}
		if nip.nodeBlockMap[key].MemPerInstanceOffset > 0.0 {
			memPerInstanceOffset = nip.nodeBlockMap[key].MemPerInstanceOffset
		}
	}
	return cpuPerInstance, memPerInstance, cpuPerInstanceOffset, memPerInstanceOffset
}

// UpdateResources 更新资源数据, 给定从operators获取的节点信息列表, 将其信息与当前的资源信息进行整合同步
// - 已经消失的节点: 剔除
// - 新出现的节点: 增加
// - 更新的节点: 更新资源信息
func (nip *NodeInfoPool) UpdateResources(nodeInfoList []*NodeInfo) {
	nip.Lock()
	defer nip.Unlock()

	newBlockMap := make(map[string]*NodeInfoBlock, 1000)
	for _, NodeInfo := range nodeInfoList {
		go recordResource(NodeInfo)

		if NodeInfo.Disabled {
			continue
		}

		if !NodeInfo.valid() {
			blog.Warnf("crm: get node(%s) resources less than 0, cpu left: %.2f, memory left:%.2f, disk left:%.2f",
				NodeInfo.Hostname, NodeInfo.CPUUsed, NodeInfo.MemUsed, NodeInfo.DiskUsed)
			continue
		}

		key := getBlockKey(NodeInfo.Attributes)
		if _, ok := newBlockMap[key]; !ok {
			newBlockMap[key] = &NodeInfoBlock{}
		}

		newBlock := newBlockMap[key]
		newBlock.DiskTotal += NodeInfo.DiskTotal
		newBlock.MemTotal += NodeInfo.MemTotal
		newBlock.CPUTotal += NodeInfo.CPUTotal
		newBlock.DiskUsed += NodeInfo.DiskUsed
		newBlock.MemUsed += NodeInfo.MemUsed
		newBlock.CPUUsed += NodeInfo.CPUUsed
		newBlock.DiskLeft += NodeInfo.DiskLeft
		newBlock.MemLeft += NodeInfo.MemLeft
		newBlock.CPULeft += NodeInfo.CPULeft
		//inherit the instance model if exist
		cpuPerInstance, memPerInstance, cpuPerInstanceOffset, memPerInstanceOffset := nip.getNodeInstance(key)
		newBlock.AvailableInstance += NodeInfo.FigureAvailableInstanceFromFree(
			cpuPerInstance,
			memPerInstance,
			nip.diskPerInstance,
		)
		newBlock.CPUPerInstance = cpuPerInstance
		newBlock.MemPerInstance = memPerInstance
		newBlock.CPUPerInstanceOffset = cpuPerInstanceOffset
		newBlock.MemPerInstanceOffset = memPerInstanceOffset

		// inherit the no-ready instance records
		if _, ok := nip.nodeBlockMap[key]; ok {
			newBlock.noReadyInstance = nip.nodeBlockMap[key].noReadyInstance
		}
	}

	nip.nodeBlockMap = make(map[string]*NodeInfoBlock, 1000)
	for key, newBlock := range newBlockMap {
		if _, ok := nip.nodeBlockMap[key]; !ok {
			nip.nodeBlockMap[key] = &NodeInfoBlock{}
		}

		nodeBlock := nip.nodeBlockMap[key]
		nodeBlock.DiskTotal = newBlock.DiskTotal
		nodeBlock.MemTotal = newBlock.MemTotal
		nodeBlock.CPUTotal = newBlock.CPUTotal
		nodeBlock.DiskUsed = newBlock.DiskUsed
		nodeBlock.MemUsed = newBlock.MemUsed
		nodeBlock.CPUUsed = newBlock.CPUUsed
		nodeBlock.DiskLeft = newBlock.DiskLeft
		nodeBlock.MemLeft = newBlock.MemLeft
		nodeBlock.CPULeft = newBlock.CPULeft
		nodeBlock.CPUPerInstance = newBlock.CPUPerInstance
		nodeBlock.MemPerInstance = newBlock.MemPerInstance
		nodeBlock.CPUPerInstanceOffset = newBlock.CPUPerInstanceOffset
		nodeBlock.MemPerInstanceOffset = newBlock.MemPerInstanceOffset
		nodeBlock.AvailableInstance = newBlock.AvailableInstance
		nodeBlock.noReadyInstance = newBlock.noReadyInstance
	}

	// record the last update time
	nip.lastUpdateTime = time.Now()
}

// GetFreeInstances 在资源池中尝试获取可用的instance, 给定需求条件condition和资源数量函数function
func (nip *NodeInfoPool) GetFreeInstances(
	condition map[string]string,
	function InstanceFilterFunction,
	caller string) (int, string, error) {

	nip.Lock()
	defer nip.Unlock()

	key := getBlockKey(condition)
	nodeBlock, ok := nip.nodeBlockMap[key]
	if !ok {
		return 0, key, engine.ErrorNoEnoughResources
	}

	need, err := function(nodeBlock.AvailableInstance - nodeBlock.noReadyInstance)
	if err != nil {
		return 0, key, err
	}

	if need+nodeBlock.noReadyInstance > nodeBlock.AvailableInstance {
		return 0, key, engine.ErrorNoEnoughResources
	}

	nodeBlock.noReadyInstance += need
	addNoReadyInfo(key, caller, int32(need))
	blog.V(5).Infof(
		"crm: get free instances consume %d instances from %s, current stats: report %d, no-ready: %d",
		need, key, nodeBlock.AvailableInstance, nodeBlock.noReadyInstance,
	)
	return need, key, nil
}

// ReleaseNoReadyInstance 消除给定区域的noReady计数, 表示这部分已经ready或已经释放
func (nip *NodeInfoPool) ReleaseNoReadyInstance(key string, instance int, caller string) {
	nip.Lock()
	defer nip.Unlock()

	nodeBlock, ok := nip.nodeBlockMap[key]
	if !ok {
		return
	}

	nodeBlock.noReadyInstance -= instance
	addNoReadyInfo(key, caller, int32(instance*-1))
	blog.V(5).Infof("crm: release %d no-ready instance from %s, current stats no-ready: %d",
		instance, key, nodeBlock.noReadyInstance)
}

// NodeInfoBlock 描述了一个特定区域的资源信息, 通常由多个区域组成一个完整的资源池NodeInfoPool
// 例如 shenzhen区, shanghai区, projectA区等等, 同一个NodeInfoBlock内的资源是统一处理的, 拥有共同的noReady计数
type NodeInfoBlock struct {
	DiskTotal            float64
	MemTotal             float64
	CPUTotal             float64
	DiskUsed             float64
	MemUsed              float64
	CPUUsed              float64
	DiskLeft             float64
	MemLeft              float64
	CPULeft              float64
	CPUPerInstance       float64
	MemPerInstance       float64
	CPUPerInstanceOffset float64
	MemPerInstanceOffset float64
	AvailableInstance    int

	noReadyInstance int
}

// ServiceInfo 描述了已经消费了资源的服务信息
type ServiceInfo struct {
	Status             ServiceStatus
	Message            string
	RequestInstances   int
	CurrentInstances   int
	AvailableEndpoints []*Endpoint
}

type ServiceStatus int

const (
	// Container Service launched and not ready.
	ServiceStatusStaging ServiceStatus = iota

	// Container Service running successfully.
	ServiceStatusRunning

	// Container Service failed to be running.
	ServiceStatusFailed
)

// String get service status string.
func (ss ServiceStatus) String() string {
	return serviceStatusMap[ss]
}

var serviceStatusMap = map[ServiceStatus]string{
	ServiceStatusStaging: "staging",
	ServiceStatusRunning: "running",
	ServiceStatusFailed:  "failed",
}

// Endpoint 描述了单个消费了资源instance的服务的对外暴露的地址信息
type Endpoint struct {
	IP    string
	Ports map[string]int
	Name  string
}

func getInstanceKey(attributes map[string]string) (string, string) {
	city, ok := attributes[AttributeKeyCity]
	if !ok || city == "" {
		city = "unknown_city"
	}

	platform, _ := attributes[AttributeKeyPlatform]
	if platform == "" {
		platform = "default-platform"
	}
	return platform, city
}

func getBlockKey(attributes map[string]string) string {
	platform, city := getInstanceKey(attributes)
	return platform + "/" + city
}

func recordResource(node *NodeInfo) {
	metricLabels := controllers.ResourceStatusLabels{
		IP: node.IP, Zone: fmt.Sprintf("crm_%s", getBlockKey(node.Attributes)),
	}

	if node.Disabled {
		selfMetric.ResourceStatusController.UpdateCPUTotal(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateCPUUsed(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateCPULeft(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateMemTotal(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateMemUsed(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateMemLeft(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateDiskTotal(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateDiskUsed(metricLabels, 0)
		selfMetric.ResourceStatusController.UpdateDiskLeft(metricLabels, 0)
		return
	}

	selfMetric.ResourceStatusController.UpdateCPUTotal(metricLabels, node.CPUTotal)
	selfMetric.ResourceStatusController.UpdateCPUUsed(metricLabels, node.CPUUsed)
	selfMetric.ResourceStatusController.UpdateCPULeft(metricLabels, node.CPULeft)
	selfMetric.ResourceStatusController.UpdateMemTotal(metricLabels, node.MemTotal)
	selfMetric.ResourceStatusController.UpdateMemUsed(metricLabels, node.MemUsed)
	selfMetric.ResourceStatusController.UpdateMemLeft(metricLabels, node.MemLeft)
	selfMetric.ResourceStatusController.UpdateDiskTotal(metricLabels, node.DiskTotal)
	selfMetric.ResourceStatusController.UpdateDiskUsed(metricLabels, node.DiskUsed)
	selfMetric.ResourceStatusController.UpdateDiskLeft(metricLabels, node.DiskLeft)
}

{{- if .Values.server.enabled -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.fullname" . }}-server
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: server
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: server
  replicas: 1
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: server
        {{- if .Values.server.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.server.podLabels "context" $) | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ template "bktbs.serviceAccountName" . }}
      {{- include "bktbs.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.server.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.server.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.server.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.server.podAffinityPreset "component" "server" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.server.podAntiAffinityPreset "component" "server" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.server.nodeAffinityPreset.type "key" .Values.server.nodeAffinityPreset.key "values" .Values.server.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.server.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.server.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.server.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.priorityClassName }}
      priorityClassName: {{ .Values.server.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.server.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.server.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      containers:
        - name: server
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.server.image "global" .Values.global) }}
          imagePullPolicy: {{ .Values.server.image.pullPolicy }}
          {{- if .Values.server.resources }}
          resources: {{- toYaml .Values.server.resources | nindent 12 }}
          {{- end }}
          command: ["/data/workspace/start.sh"]
          args:
            - -f
            - /data/config/server.json
          env:
            - name: BK_TBS_LOCAL_IP
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          ports:
            - name: http
              containerPort: 30111
              protocol: TCP
          volumeMounts:
            - name: config-volume
              mountPath: /data/config/
            - name: template-volume
              mountPath: /data/template/
          livenessProbe:
            httpGet:
              path: api/v2/health
              port: http
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: api/v2/health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
            successThreshold: 1
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "common.names.fullname" . }}-server-config
        - name: template-volume
          configMap:
            name: {{ include "common.names.fullname" . }}-server-template
{{- end }}
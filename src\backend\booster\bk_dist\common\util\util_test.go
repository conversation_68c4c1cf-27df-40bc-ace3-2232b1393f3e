/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package util

import (
	"fmt"
	"os"
	"testing"
)

func TestLZ4(t *testing.T) {
	tests := []struct {
		casename string
		f        string
		want     string
	}{
		{"wrong_file", "C:\\Users\\<USER>\\Downloads\\Module.Slate.2.cpp.obj", "uncompress failed"},
	}

	for _, tt := range tests {
		data, err := os.ReadFile(tt.f)
		if err != nil {
			fmt.Printf("failed to read file[%s]\n", tt.f)
			continue
		}

		t.Run(tt.casename, func(t *testing.T) {
			zipdata, err := Lz4Compress(data)
			fmt.Printf("compressed [%d] to [%d] with err:%v\r\n", len(data), len(zipdata), err)

			if err == nil {
				dst := make([]byte, len(data))
				unzipdata, err := Lz4Uncompress(zipdata, dst)
				fmt.Printf("uncompressed [%d] to [%d] with err:%v\r\n", len(zipdata), len(unzipdata), err)
			}
		})
	}
}

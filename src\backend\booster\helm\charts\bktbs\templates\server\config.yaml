{{- if .Values.server.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.fullname" . }}-server-config
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: server
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
data:
  server.json: |-
    {
      "etcd_endpoints": "{{ include "bktbs.etcd.address" . }}",
      "etcd_root_path": "{{ .Values.server.etcdRootPath }}",
      "etcd_ca_file": "",
      "etcd_cert_file": "",
      "etcd_key_file": "",
      "etcd_key_password": "",
      "v": {{ .Values.server.log.verbose }},
      "alsologtostderr": {{ default "true" .Values.server.log.alsoLogToStdErr }},
      "address": "0.0.0.0",
      "port": 30111,
      "metric_port": 30112,
      "disttask_queue_list": {{ toJson .Values.server.disttask_queue_list }},
      "engine_disttask": {
        "engine_disttask_enable": true,
        "engine_disttask_mysql": "{{ include "bktbs.mariadb.address" . }}",
        "engine_disttask_mysql_db": "{{ include "bktbs.mariadb.database" . }}",
        "engine_disttask_mysql_user": "{{ include "bktbs.mariadb.username" . }}",
        "engine_disttask_mysql_pwd": "{{ include "bktbs.mariadb.password" . }}"
      },
      "k8s_container_resource": {
        "crm_enable": true,
        "crm_operator": "k8s",
        "crm_bcs_api_token": "{{ .Values.server.bcs.apiToken }}",
        "crm_bcs_api_address": "{{ .Values.server.bcs.apiAddress }}",
        "crm_bcs_cpu_per_instance": {{ .Values.server.bcs.cpuPerInstance }},
        "crm_bcs_mem_per_instance": {{ .Values.server.bcs.memPerInstance }},
        "crm_bcs_group_label_key": "{{ .Values.server.bcs.groupLabelKey }}",
        "crm_bcs_platform_label_key": "{{ .Values.server.bcs.platformLabelKey }}",
        "crm_bcs_disable_win_host_network": {{ .Values.server.bcs.disableWinHostNetwork }},
        "crm_bcs_cluster_id": "{{ .Values.server.bcs.clusterID }}",
        "crm_bcs_template_file": "/data/template/template.yaml",
        "crm_resource_mysql": "{{ include "bktbs.mariadb.address" . }}",
        "crm_resource_mysql_db": "{{ include "bktbs.mariadb.database" . }}",
        "crm_resource_mysql_table": "bcs_k8s_resource",
        "crm_resource_mysql_user": "{{ include "bktbs.mariadb.username" . }}",
        "crm_resource_mysql_pwd": "{{ include "bktbs.mariadb.password" . }}"
      }
    }
{{- end }}

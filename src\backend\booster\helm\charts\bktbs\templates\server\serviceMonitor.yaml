{{- if .Values.server.enabled -}}
{{- if .Values.ServiceMonitor.enable -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "common.names.fullname" . }}-servicemonitor
  namespace: {{ .Release.Namespace }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    - interval: 60s # 采集周期
      path: /metrics # 指标接口路径
      port: metrics # service的端口名，必须使用端口名，不能使用数字 
  namespaceSelector:
    any: true
  selector: # 过滤出需要采集的service
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: server
{{- end -}}
{{- end -}}
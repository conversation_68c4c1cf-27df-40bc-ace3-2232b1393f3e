images:
  - name: tlinux1.2-clang2.8
    base: bcs/distcc-base/tlinux1.2_clang_2.8_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-clang2.8:v3
    tmpl:
    note:

  - name: tlinux1.2-gcc4.1.2
    base: bcs/distcc-base/tlinux1.2_gcc_4.1.2_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc4.1.2:v3
    tmpl:
    note:

  - name: tlinux1.2-gcc4.1.2-tvm
    base: bcs/bcss-distccd-tvm4.1.2:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc4.1.2-tvm:v3
    tmpl:
    note: custom image for kg

  - name: tlinux1.2-gcc4.4.6-4
    base: bcs/distcc-base/tlinux1.2_gcc_4.4.6_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc4.4.6-4:v3
    tmpl:
    note:

  - name: tlinux1.2-gcc4.8.2-15-happybuild
    base: bcs/distcc-base/tlinux1.2_happybuild_4.8.2_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc4.8.2-15-happybuild:v3
    tmpl:
    note: custom image for happy-studio

  - name: tlinux1.2-gcc4.8.2-mmtest
    base: bcs/distcc-base/mmtest4.8.2_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc4.8.2-mmtest:v3
    tmpl:
    note: custom image for wechat-test

  - name: tlinux1.2-gcc7.3.0-mmtest
    base: bcs/distcc-base/mmtest7.3.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc7.3.0-mmtest:v3
    tmpl:
    note: custom image for wechat-test

  - name: tlinux1.2-gcc7.5.0-mmtest
    base: bcs/distcc-base/tlinux1.2_mmtest7.5.0_distccd:v2
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc7.5.0-mmtest:v3
    tmpl:
    note: custom image for wechat-test

  - name: tbs-worker-tlinux1.2-gcc4.9.3-wework
    base: bcs/distcc-base/tlinux1.2_gcc4.9.3_wework:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux1.2-gcc4.9.3-wework:v3
    tmpl:
    note: custom image for wework

  - name: tlinux2.2-ndk-r14b
    base: bcs/distcc-base/tlinux2.2_ndk_r14b:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-ndk-r14b:v3
    tmpl: ndk/Dockerfile.template
    note: custom image for ndk

  - name: tlinux2.2-ndk-r21e
    base: bcs/distcc-base/tlinux2.2_ndk_r21e:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-ndk-r21e:v3
    tmpl: ndk/Dockerfile.template
    note: custom image for ndk

  - name: tlinux2.2-clang2.8
    base: bcs/distcc-base/tlinux1.2_clang_2.8_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-clang2.8:v3
    tmpl:
    note:

  - name: tlinux2.2-clang3.4.2
    base: bcs/distcc-base/tlinux2.2_clang_3.4.2_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-clang3.4.2:v3
    tmpl:
    note:

  - name: tlinux2.2-clang3.5.2
    base: bcs/distcc-base/tlinux2.2_clang_3.5.2_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-clang3.5.2:v3
    tmpl:
    note:

  - name: tlinux2.2-clang3.6.0
    base: bcs/distcc-base/tlinux2.2_clang_3.6.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-clang3.6.0:v3
    tmpl:
    note:

  - name: tlinux2.2-clang5.0.1
    base: bcs/distcc-base/tlinux2.2_clang_5.0.1_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-clang5.0.1:v3
    tmpl:
    note:

  - name: tlinux2.2-clang10.0.1
    base: bcs/distcc-base/tlinux2.2_clang_10.0.1:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-clang10.0.1:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc4.8.2
    base: bcs/distcc-base/tlinux2.2_gcc_4.8.2:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.8.2:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc4.8.2-mmtest
    base: bcs/distcc-base/tlinux2.2_gcc_4.8.2:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.8.2-mmtest:v3
    tmpl:
    note: custom image for wechat-test

  - name: tlinux2.2-gcc4.8.5-4
    base: bcs/distcc-base/tlinux2.2_gcc_4.8.5-4:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.8.5-4:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc4.8.5-5
    base: bcs/distcc-base/tlinux2.2_gcc_4.8.5_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.8.5-5:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc4.9.0
    base: bcs/distcc-base/tlinux2.2_gcc_4.9.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.9.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc4.9.3
    base: bcs/distcc-base/tlinux2.2_gcc_4.9.3_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.9.3:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc4.9.4
    base: bcs/distcc-base/tlinux2.2_gcc_4.9.4_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc4.9.4:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc5.4.0
    base: bcs/distcc-base/tlinux2.2_gcc_5.4.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc5.4.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc5.5.0
    base: bcs/distcc-base/tlinux2.2_gcc_5.5.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc5.5.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc6.2.0
    base: bcs/distcc-base/tlinux2.2_gcc_6.2.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc6.2.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc7.1.0
    base: bcs/distcc-base/tlinux2.2_gcc_7.1.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.1.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc7.3.0
    base: bcs/distcc-base/tlinux2.2_gcc_7.3.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.3.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc7.3.0-mmtest
    base: bcs/distcc-base/tlinux2.2_gcc_7.3.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.3.0-mmtest:v3
    tmpl:
    note: custom image for wechat-test

  - name: tlinux2.2-gcc7.3.1-5
    base: bcs/distcc-base/tlinux2.2_gcc_7.3.1_distccd:v2
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.3.1-5:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc7.3.1-6
    base: bcs/distcc-base/tlinux2.2_gcc_7.3.1-6:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.3.1-6:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc7.4.0
    base: bcs/distcc-base/tlinux2.2_gcc_7.4.0_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.4.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc7.5.0-mmtest
    base: bcs/distcc-base/tlinux2.2_mmtest7.5.0_distccd:v2
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc7.5.0-mmtest:v3
    tmpl:
    note: custom image for wechat-test

  - name: tlinux2.2-gcc8.2.0
    base: bcs/distcc-base/tlinux2.2_gcc_8.2.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc8.2.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc8.3.0
    base: bcs/distcc-base/tlinux2.2_gcc_8.3.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc8.3.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc8.4.0
    base: bcs/distcc-base/tlinux2.2_gcc_8.4.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc8.4.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc9.3.1
    base: bcs/distcc-base/tlinux2.2_gcc_9.3.1:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc9.3.1:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc10.2.0
    base: bcs/distcc-base/tlinux2.2_gcc_10.2.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc10.2.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc10.3.0
    base: bcs/distcc-base/tlinux2.2_gcc_10.3.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc10.3.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc11.1.0
    base: bcs/distcc-base/tlinux2.2_gcc_11.1.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc11.1.0:v3
    tmpl:
    note:

  - name: tlinux2.2-gcc12.0.0
    base: bcs/distcc-base/tlinux2.2_gcc_12.0.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux2.2-gcc12.0.0:v3
    tmpl:
    note:

  - name: tlinux3.1-clang8.0.1
    base: bcs/distcc-base/tlinux3.1_clang8.0.1_distccd:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.1-clang8.0.1:v3
    tmpl:
    note:

  - name: tlinux3.2-clang9.0.1
    base: bcs/distcc-base/tlinux3.2_gcc_8.4.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-clang9.0.1:v3
    tmpl:
    note:

  - name: tlinux3.2-clang10.0.1
    base: bcs/distcc-base/tlinux3.2_clang_10.0.1:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-clang10.0.1:v3
    tmpl:
    note:

  - name: tlinux3.2-gcc8.3.0
    base: bcs/distcc-base/tlinux3.2_gcc_8.3.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-gcc8.3.0:v3
    tmpl:
    note:

  - name: tlinux3.2-gcc8.3.1-5
    base: bcs/distcc-base/tlinux3.2_gcc_8.3.1-5:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-gcc8.3.1-5:v3
    tmpl:
    note:

  - name: tlinux3.2-gcc8.4.0
    base: bcs/distcc-base/tlinux3.2_gcc_8.4.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-gcc8.4.0:v3
    tmpl:
    note:

  - name: tlinux3.2-gcc9.2.0
    base: bcs/distcc-base/tlinux3.2_gcc_9.2.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-gcc9.2.0:v3
    tmpl:
    note:

  - name: tlinux3.2-gcc11.1.0
    base: bcs/distcc-base/tlinux3.2_gcc_11.1.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-gcc11.1.0:v3
    tmpl:
    note:

  - name: tlinux3.2-ue4.25.0
    base: bcs/distcc-base/tlinux3.2-ue4.25.0:v1
    trgt: bcs/disttask/linux/tbs-worker-tlinux3.2-ue4.25.0:v3
    tmpl:
    note:
{
  "restartPolicy": {
    "backoff": 1,
    "maxtimes": 100,
    "policy": "Never",
    "interval": 5
  },
  "killPolicy": {
    "gracePeriod": 10
  },
  "constraint": {
    "intersectionItem": [__crm_constraint__]
  },
  "apiVersion": "v1",
  "kind": "application",
  "metadata": {
    "name": "__crm_name__",
    "namespace": "__crm_namespace__",
    "labels": {}
  },
  "spec": {
    "template": {
      "metadata": {
        "name": "__crm_name__",
        "namespace": "__crm_namespace__",
        "labels": {}
      },
      "spec": {
        "containers": [
          {
            "type": "MESOS",
            "privileged": false,
            "env": [__crm_env__],
            "ports": [__crm_ports__],
            "resources": {
              "limits": {
                "cpu": "__crm_limit_cpu__",
                "memory": "__crm_limit_mem__"
              },
              "requests": {
                "cpu": "__crm_cpu__",
                "memory": "__crm_mem__"
              }
            },
            "image": "__crm_image__",
            "imagePullPolicy": "IfNotPresent"
          }
        ],
        "networkMode": "HOST",
        "networkType": "cnm"
      }
    },
    "instance": __crm_instance__
  }
}
/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package command

// const vars
const (
	ClientUsage = "BlueKing net check tool"
	ClientName  = "net-check-tool"
)

// Name return client name
func Name() string {
	return ClientName
}

// Usage return client usage
func Usage() string {
	return ClientUsage
}

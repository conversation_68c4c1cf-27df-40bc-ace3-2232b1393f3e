{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"mariadb": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否部署mariadb", "form": true}}}, "externalDatabase": {"type": "object", "title": "外部mariadb服务配置", "form": true, "hidden": "mongodb/enabled", "properties": {"host": {"type": "string", "title": "目标地址", "form": true}, "port": {"type": "integer", "title": "端口", "form": true}, "usernmae": {"type": "string", "title": "用户名", "form": true}, "password": {"type": "string", "title": "密码", "form": true}, "database": {"type": "string", "title": "数据库", "form": true}}}, "ingress": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否创建ingress", "form": true}}}, "nginx-ingress-controller": {"type": "object", "title": "nginx-ingress-controller配置", "form": true, "hidden": {"value": false, "path": "ingress/enabled"}, "properties": {"enabled": {"type": "boolean", "title": "是否部署nginx-ingress-controller", "form": true}}}, "server": {"type": "object", "title": "server配置", "description": "tbs-server", "form": true, "properties": {"host": {"type": "string", "title": "域名", "description": "server必须通过域名访问", "form": true}, "enabled": {"type": "boolean", "title": "是否部署server", "form": true}}}, "gateway": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否部署gateway", "form": true}}}, "dashboard": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否部署dashboard", "form": true}}}}}
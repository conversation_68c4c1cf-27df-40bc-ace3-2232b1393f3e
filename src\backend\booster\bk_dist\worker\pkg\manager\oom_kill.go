/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package manager

import (
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/TencentBlueKing/bk-turbo/src/backend/booster/common/blog"
)

const (
	keyProcessId       = "ProcessId"
	keyParentProcessId = "ParentProcessId"
	keyName            = "Name"
	keyWorkingSetSize  = "WorkingSetSize"
	keyCreationDate    = "CreationDate"

	keyNum = 5 + 1 // query keys and default node

	// kill 权重为下面两个权重的和
	killWeightOfMemory = 10 // 内存占用/最小内存*该权重

	// killWeightMemoryUnit = 1024 * 1024 * 1024 //
	killWeightMemoryUnit = 100 * 1024 * 1024 // 测试时先改到100MB

	killWeightOfDate = 100 // 执行时长/30秒*该权重*(-1)
	killDurationUnit = 30  // 秒

	minKillWeight = -99999999
)

func getQueryHeaders() string {
	return fmt.Sprintf("%s,%s,%s,%s,%s", keyProcessId, keyParentProcessId, keyName, keyWorkingSetSize, keyCreationDate)
}

type process struct {
	name string
	pid  string
	ppid string

	// 单位是字节
	workingSetSize int64

	// YYYYMMDDHHMMSS.ffffff+zzz，表示年、月、日、小时、分钟、秒、毫秒和时区偏移量
	// 比如：20231116020521.072703+480
	creationDate string
	creationTime time.Time
	duration     int64 // 运行的时长，当前考虑精确到秒数

	killWeight int64

	printed bool

	children []*process
}

func (p *process) toString() string {
	return fmt.Sprintf("%s %s %s %d %s running %d seconds weight %d",
		p.name, p.pid, p.ppid, p.workingSetSize, p.creationTime, p.duration, p.killWeight)
}

// 计算得到的kill权重，越大越可能被kill
func (p *process) calcKillWeight() int64 {
	if p.workingSetSize < killWeightMemoryUnit {
		return minKillWeight
	}

	return (p.workingSetSize)/killWeightMemoryUnit*killWeightOfMemory - p.duration/killDurationUnit*killWeightOfDate
}

func logProcess(p *process, indentation int) {
	if p.printed {
		return
	}

	indent := " "
	if indentation > 0 {
		for i := 0; i < indentation; i++ {
			indent += "\t"
		}
	}
	blog.Infof("log child process:%s %s\n", indent, p.toString())
	p.printed = true

	for _, pc := range p.children {
		logProcess(pc, indentation+1)
	}
}

func getIndexFromHeader(lines []string) (nameindex, pidindex, ppidindex, wsindex, cdindex int, err error) {
	for _, l := range lines {
		if !strings.Contains(l, keyParentProcessId) {
			continue
		}
		filelds := strings.Split(strings.Trim(l, "\r\n "), ",")
		for i, v := range filelds {
			switch v {
			case keyProcessId:
				pidindex = i
				break
			case keyParentProcessId:
				ppidindex = i
				break
			case keyName:
				nameindex = i
				break
			case keyWorkingSetSize:
				wsindex = i
				break
			case keyCreationDate:
				cdindex = i
				break
			}
		}
		return
	}

	err = fmt.Errorf("not found headers")
	return
}

func parseDate(creationDate string, loc *time.Location) time.Time {
	// blog.Infof("parse date:%s", creationDate)
	layout := "20060102150405.000000"
	creationTime, err := time.ParseInLocation(layout, creationDate[:len(layout)], loc)
	if err != nil {
		blog.Infof("resolve %s failed with error:%v\r\n", creationDate, err)
		return time.Now()
	}

	return creationTime
}

func getAllChildren(p *process, arr *[]*process) error {
	if len(p.children) > 0 {
		for _, pc := range p.children {
			*arr = append(*arr, pc)
			// blog.Infof("appended child process: %s", pc.toString())
			getAllChildren(pc, arr)
		}
	}

	return nil
}

func getChildren() ([]*process, error) {
	if runtime.GOOS != "windows" {
		return nil, fmt.Errorf("only implement on windows now, you can install pstree on others")
	}

	start := time.Now()

	blog.Debugf("getChildren ready run wmic now")
	out, err := exec.Command("wmic", "process", "get", getQueryHeaders(), "/format:csv").Output()
	if err != nil {
		blog.Warnf("执行命令出错:", err)
		return nil, err
	}
	// blog.Infof("getChildren run wmic got output:%s", string(out))

	lines := strings.Split(string(out), "\n")
	// get right result index for each key
	nindex, pindex, ppindex, wsindex, cdindex, err := getIndexFromHeader(lines)
	if err != nil {
		blog.Warnf("getChildren got headers from output failed with error:%v", err)
		return nil, err
	}

	processarr := make([]*process, 0, 100)
	now := time.Now()
	for _, l := range lines {
		if strings.Contains(l, keyParentProcessId) {
			continue
		}

		filelds := strings.Split(strings.Trim(l, "\r\n "), ",")
		if len(filelds) >= keyNum {
			creationDate := filelds[cdindex]
			creationTime := parseDate(creationDate, time.Local)
			workingSetSize, _ := strconv.ParseInt(filelds[wsindex], 10, 64)
			p := process{
				name:           filelds[nindex],
				ppid:           filelds[ppindex],
				pid:            filelds[pindex],
				workingSetSize: workingSetSize,
				creationDate:   creationDate,
				creationTime:   creationTime,
				duration:       now.Unix() - creationTime.Unix(),
			}
			p.killWeight = p.calcKillWeight()

			processarr = append(processarr, &p)
		}
	}
	blog.Infof("getChildren run wmic got total %d process", len(processarr))

	for _, p := range processarr {
		for _, p1 := range processarr {
			if p1.ppid == p.pid && p.pid != p.ppid {
				// 容器里面的 pid 不严谨，系统进程（csrss.exe/wininit.exe）的父进程id可能被业务进程复用
				// 所以额外增加创建时间判断，保证父进程创建时间早于子进程
				if p.duration > p1.duration {
					if p.children == nil {
						p.children = make([]*process, 0, 10)
					}

					p.children = append(p.children, p1)
				}
			}
		}
	}

	children := make([]*process, 0, 72)
	curpid := fmt.Sprintf("%d", os.Getpid())
	for _, p := range processarr {
		if p.pid == curpid {
			logProcess(p, 0)
			getAllChildren(p, &children)
		}
	}

	spent := time.Now().Unix() - start.Unix()
	blog.Infof("getChildren got total %d children process after %d seconds",
		len(children), spent)

	return children, nil
}

func getMaxWeight(arr []*process) *process {
	var maxweightp *process = nil
	for _, p := range arr {
		if p.killWeight > minKillWeight {
			if maxweightp == nil {
				maxweightp = p
			} else if p.killWeight > maxweightp.killWeight {
				maxweightp = p
			}
		}
	}

	return maxweightp
}

func KillProcess(p *process) error {
	if p == nil {
		return nil
	}

	blog.Infof("ready kill process:%s", p.toString())

	cmd := exec.Command("taskkill", "/f", "/t", "/PID", p.pid)
	err := cmd.Run()
	if err != nil {
		blog.Infof("[taskkill /f /t /PID %s] failed with err[%v] for process:%s", p.pid, err, p.toString())
		return err
	} else {
		blog.Infof("[taskkill /f /t /PID %s] succeed for process:%s", p.pid, p.toString())
		return nil
	}

	// return nil
}

func killOneChild() error {
	blog.Infof("ready kill one child now...")

	children, err := getChildren()
	if err != nil {
		return err
	}

	if len(children) > 0 {
		p := getMaxWeight(children)
		if p != nil {
			KillProcess(p)
		}
	}

	return nil
}

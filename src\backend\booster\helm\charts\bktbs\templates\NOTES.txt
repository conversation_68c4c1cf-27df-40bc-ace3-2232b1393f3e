Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }}.

验证步骤:

1. 配置{{ .Values.server.host }}域名解析(如果域名可以被DNS正确解析，您可以跳过此步骤)

    {{ if .Values.ingress.enabled }}
    - 添加映射记录到/etc/hosts, <IngressIP>为您的Ingress外部ip

        echo "<IngressIP>  {{ .Values.server.host }}" >> /etc/hosts

    {{- else if contains "ClusterIP" .Values.gateway.service.type }}
    - 通过port-forward暴露repo-gateway

        kubectl port-forward --namespace {{ .Release.Namespace }} service/{{ include "common.names.fullname" . }}-gateway 80:80

    - 添加映射记录到/etc/hosts

        echo "127.0.0.1  {{ .Values.gateway.host }}" >> /etc/hosts
        {{- if .Values.docker.enabled }}
        echo "127.0.0.1  docker.{{ .Values.gateway.host }}" >> /etc/hosts
        {{- end }}
        {{- if .Values.helm.enabled }}
        echo "127.0.0.1  helm.{{ .Values.gateway.host }}" >> /etc/hosts
        {{- end }}
    {{- end }}

2. 访问http://{{ .Values.server.host }}/dashboard 验证服务器是否能正常访问

    **TIP** 如果使用NodePort或port-forward方式访问，请注意添加端口

3. 在加速用的bcs集群内, 给加速节点配置上 {{ .Values.server.bcs.groupLabelKey }}=default 的label
   这样在tbs-server pod的日志里，就能看到City: linux/default的节点信息，表明加速资源就绪
   例如：kubectl label node ******** bk-turbo-az=default

4. 在加速用的bcs集群内, 手动创建一个namespace disttask
   例如：kubectl create ns disttask

5. 在配置完saas的编译环境之后, 把对应的配置写入tbs
   curl -XPUT -d '{"data": {"image":"${image}"}, "operator":"admin"}' http://{{ .Values.server.host }}/gateway/api/v1/disttask-cc/resource/worker/${worker_version}
   其中${image}是加速镜像的地址, ${worker_version}是在saas上配置的环境名称


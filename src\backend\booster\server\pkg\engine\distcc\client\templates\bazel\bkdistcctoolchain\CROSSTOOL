# toolchain/CROSSTOOL

major_version: "1"
minor_version: "0"
default_target_cpu: "cpudistcc"

toolchain {
    toolchain_identifier: "cross-distcc-toolchain"
    host_system_name: "i686-unknown-linux-gnu"
    target_system_name: "asmjs-unknown-emscripten"
    target_cpu: "cpudistcc"
    target_libc: "unknown"
    compiler: "distcccompile"
    abi_version: "unknown"
    abi_libc_version: "unknown"

    tool_path { name: "cpp" path: "/usr/bin/cpp" }
    tool_path { name: "ar" path: "/usr/bin/ar" }
    tool_path { name: "gcc" path: "wrapper_cc.sh" }
    tool_path { name: "g++" path: "wrapper_cxx.sh" }
    tool_path { name: "clang" path: "wrapper_cc.sh" }
    tool_path { name: "clang++" path: "wrapper_cxx.sh" }
    tool_path { name: "gcov" path: "/usr/bin/gcov" }
    tool_path { name: "ld" path: "/usr/bin/ld" }
    tool_path { name: "nm" path: "/usr/bin/nm" }
    tool_path { name: "objdump" path: "/usr/bin/objdump" }
    tool_path { name: "strip" path: "/usr/bin/strip" }

    cxx_flag: "-std=c++0x"
    
    compiler_flag: "-U_FORTIFY_SOURCE"
    compiler_flag: "-fstack-protector"
    compiler_flag: "-fPIC"
    compiler_flag: "-Wall"
    compiler_flag: "-Wunused-but-set-parameter"
    compiler_flag: "-Wno-free-nonheap-object"
    compiler_flag: "-fno-omit-frame-pointer"

    unfiltered_cxx_flag: "-fno-canonical-system-headers"
    unfiltered_cxx_flag: "-Wno-builtin-macro-redefined"
    unfiltered_cxx_flag: "-D__DATE__=\"redacted\""
    unfiltered_cxx_flag: "-D__TIMESTAMP__=\"redacted\""
    unfiltered_cxx_flag: "-D__TIME__=\"redacted\""

    #cxx_builtin_include_directory: "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/"
    #cxx_builtin_include_directory: "/usr/include/"
    cxx_builtin_include_directory: "/usr/lib/gcc/"
    cxx_builtin_include_directory: "/usr/local/include"
    cxx_builtin_include_directory: "/usr/include"

    linker_flag: "-lstdc++"
    linker_flag: "-lm"
    linker_flag: "-lpthread"
    linker_flag: "-fuse-ld=gold"
    linker_flag: "-Wl,-no-as-needed"
    linker_flag: "-Wl,-z,relro,-z,now"
    linker_flag: "-L/lib64/"
}

default_toolchain {
    cpu: "cpudistcc"
    toolchain_identifier: "default-distcc-toolchain"
}
